<?xml version="1.0" encoding="UTF-8"?>
<svg width="54px" height="34px" viewBox="0 0 54 34" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <path id="path-1" d="M363,150 C368.522847,150 373,154.477153 373,160 C373,165.522847 368.522847,170 363,170 L343,170 C337.477153,170 333,165.522847 333,160 C333,154.477153 337.477153,150 343,150 L363,150 Z M350.86,157.566 C350.206667,157.566 349.595333,157.708333 349.026,157.993 C348.456667,158.277667 347.997,158.704667 347.647,159.274 C347.297,159.843333 347.122,160.534 347.122,161.346 C347.122,162.167333 347.297,162.867333 347.647,163.446 C347.997,164.024667 348.459,164.458667 349.033,164.748 C349.607,165.037333 350.220667,165.182 350.874,165.182 C351.583333,165.182 352.222667,165.032667 352.792,164.734 C353.361333,164.435333 353.748667,164.010667 353.954,163.46 L352.288,162.466 C352.176,162.736667 352.001,162.942 351.763,163.082 C351.525,163.222 351.256667,163.292 350.958,163.292 C350.594,163.292 350.286,163.182333 350.034,162.963 C349.782,162.743667 349.604667,162.433333 349.502,162.032 L354.136,162.032 C354.192,161.696 354.22,161.378667 354.22,161.08 C354.22,160.370667 354.08,159.75 353.8,159.218 C353.52,158.686 353.125667,158.277667 352.617,157.993 C352.108333,157.708333 351.522667,157.566 350.86,157.566 Z M358.196,155.438 L355.914,155.438 L355.914,157.762 L354.584,157.762 L354.584,159.68 L355.914,159.68 L355.914,162.158 C355.914,163.287333 356.170667,164.073667 356.684,164.517 C357.197333,164.960333 357.878667,165.182 358.728,165.182 C359.68,165.182 360.417333,164.948667 360.94,164.482 L360.142,162.9 C359.834,163.114667 359.521333,163.222 359.204,163.222 C358.812,163.222 358.546,163.093667 358.406,162.837 C358.266,162.580333 358.196,162.218667 358.196,161.752 L358.196,159.68 L360.408,159.68 L360.408,157.762 L358.196,157.762 L358.196,155.438 Z M364.86,157.566 C364.104,157.566 363.443667,157.724667 362.879,158.042 C362.314333,158.359333 361.896667,158.793333 361.626,159.344 L363.236,160.282 C363.525333,159.684667 363.992,159.386 364.636,159.386 C365.401333,159.386 365.816667,159.843333 365.882,160.758 C365.387333,160.580667 364.883333,160.492 364.37,160.492 C363.819333,160.492 363.310667,160.59 362.844,160.786 C362.377333,160.982 362.006333,161.264333 361.731,161.633 C361.455667,162.001667 361.318,162.428667 361.318,162.914 C361.318,163.380667 361.441667,163.784333 361.689,164.125 C361.936333,164.465667 362.272333,164.727 362.697,164.909 C363.121667,165.091 363.590667,165.182 364.104,165.182 C364.589333,165.182 364.990667,165.102667 365.308,164.944 C365.625333,164.785333 365.896,164.542667 366.12,164.216 C366.125333,164.253333 366.13181,164.301333 366.139429,164.36 L366.181429,164.696 C366.192476,164.786667 366.204667,164.888 366.218,165 L368.136,165 L368.136,160.912 C368.136,159.848 367.851333,159.024333 367.282,158.441 C366.712667,157.857667 365.905333,157.566 364.86,157.566 Z M341.942,154.752 L338.008,154.752 L338.008,165 L342.292,165 C343.514667,165 344.476,164.736333 345.176,164.209 C345.876,163.681667 346.226,162.951333 346.226,162.018 C346.226,160.898 345.694,160.123333 344.63,159.694 C345.358,159.246 345.722,158.564667 345.722,157.65 C345.722,156.707333 345.367333,155.988667 344.658,155.494 C343.948667,154.999333 343.043333,154.752 341.942,154.752 Z M364.846,162.032 C365.172667,162.032 365.518,162.078667 365.882,162.172 L365.882,162.354 C365.882,162.699333 365.739667,162.974667 365.455,163.18 C365.170333,163.385333 364.850667,163.488 364.496,163.488 C364.234667,163.488 364.02,163.427333 363.852,163.306 C363.684,163.184667 363.6,163.016667 363.6,162.802 C363.6,162.568667 363.712,162.382 363.936,162.242 C364.16,162.102 364.463333,162.032 364.846,162.032 Z M342.138,160.8 C342.642,160.8 343.036333,160.895667 343.321,161.087 C343.605667,161.278333 343.748,161.542 343.748,161.878 C343.748,162.214 343.608,162.473 343.328,162.655 C343.048,162.837 342.656,162.928 342.152,162.928 L340.346,162.928 L340.346,160.8 L342.138,160.8 Z M350.79,159.386 C351.088667,159.386 351.340667,159.479333 351.546,159.666 C351.751333,159.852667 351.872667,160.123333 351.91,160.478 L349.502,160.478 C349.604667,160.132667 349.765667,159.864333 349.985,159.673 C350.204333,159.481667 350.472667,159.386 350.79,159.386 Z M341.746,156.81 C342.222,156.81 342.590667,156.889333 342.852,157.048 C343.113333,157.206667 343.244,157.44 343.244,157.748 C343.244,158.074667 343.108667,158.336 342.838,158.532 C342.567333,158.728 342.203333,158.826 341.746,158.826 L340.346,158.826 L340.346,156.81 L341.746,156.81 Z" />
        <filter id="filter-2" x="-27.5%" y="-50.0%" width="155.0%" height="210.0%" filterUnits="objectBoundingBox">
            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1" />
            <feOffset dx="0" dy="1" in="shadowSpreadOuter1" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.562827797   0 0 0 0 0  0 0 0 0.845061189 0" type="matrix" in="shadowBlurOuter1" />
        </filter>
    </defs>
    <g transform="translate(-326.000000, -144.000000)" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1" />
        <use fill="#FFFAC7" fill-rule="evenodd" xlink:href="#path-1" />
    </g>
</svg>
