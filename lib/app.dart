import 'package:ff_annotation_route_library/ff_annotation_route_library.dart' show onGenerateRoute;
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart' show PointerDeviceKind;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart' show FlutterSmartDialog;
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;
import 'package:me_l10n/me_l10n.dart' show MELocalizations;
import 'package:me_misc/me_misc.dart' as misc;
import 'package:me_ui/me_ui.dart';

import 'constants/envs.dart' show envActive, envOverrode;
import 'constants/release.dart' show Release;
import 'constants/themes.dart';
import 'l10n/gen/app_localizations.dart';
import 'routes/card3_route.dart';
import 'routes/card3_routes.dart';

class Card3App extends ConsumerWidget {
  const Card3App({super.key});

  void _configureUIConfig(BuildContext context) {
    MEUIConfig.stepsDialogConfig = const MEUIStepsDialogConfig(
      loadingGradient: LinearGradient(
        colors: [Color(0xffd0e754), Color(0xffd0e754)],
      ),
      succeedGradient: LinearGradient(
        colors: [Color(0xffd0e754), Color(0xffd0e754)],
      ),
    );
    MEUIConfig.themeTextButtonConfig = MEUIThemeTextButtonConfig(
      borderRadius: const BorderRadius.all(Radius.circular(16.0)),
      groupOutlinedCancel: true,
      groupButtonsGap: 10.0,
      groupCancelButtonColor: context.meTheme.cardColor,
    );
  }

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer = FirebaseAnalyticsObserver(analytics: analytics);

  //   Future<void> _initializeFlutterFire() async {
  //   if (_kTestingCrashlytics) {
  //     // Force enable crashlytics collection enabled if we're testing it.
  //     await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  //   } else {
  //     // Else only enable it in non-debug builds.
  //     // You could additionally extend this to allow users to opt-in.
  //     await FirebaseCrashlytics.instance
  //         .setCrashlyticsCollectionEnabled(!kDebugMode);
  //   }

  //   if (_kShouldTestAsyncErrorOnInit) {
  //     await _testAsyncErrorOnInit();
  //   }
  // }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const locale = Locale('en');
    return MaterialApp(
      onGenerateTitle: (context) => AppLocalizations.of(context)!.appTitle,
      builder: FlutterSmartDialog.init(
        builder: (context, child) => _builder(context, ref, child),
      ),
      navigatorKey: misc.meNavigatorKey,
      scrollBehavior: const ScrollBehavior().copyWith(
        dragDevices: PointerDeviceKind.values.toSet(),
        scrollbars: false,
        overscroll: false,
      ),
      locale: locale,
      supportedLocales: AppLocalizations.supportedLocales,
      localizationsDelegates: const [
        MELocalizations.delegate,
        ...AppLocalizations.localizationsDelegates,
      ],
      navigatorObservers: <NavigatorObserver>[
        misc.meNavigatorObserver,
        misc.meRouteObserver,
      ],
      themeMode: ThemeMode.light,
      theme: themeBy(
        meTheme: defaultMEThemeLight,
        brightness: Brightness.light,
        locale: locale,
      ),
      darkTheme: themeBy(
        meTheme: defaultMEThemeDark,
        brightness: Brightness.dark,
        locale: locale,
      ),
      initialRoute: Routes.root.name,
      onGenerateRoute: (RouteSettings settings) => onGenerateRoute(
        settings: settings,
        getRouteSettings: getRouteSettings,
        notFoundPageBuilder: () => Container(
          alignment: Alignment.center,
          color: Colors.black,
          child: Text(
            '${settings.name ?? 'Unknown'} route not found',
            style: const TextStyle(color: Colors.white, inherit: false),
          ),
        ),
      ),
    );
  }

  Widget _builder(BuildContext context, WidgetRef ref, Widget? child) {
    child = LogConsole.wrap(
      GestureDetector(onTap: misc.hideKeyboard, child: child),
      enable: !kDebugMode && !Release.sealed,
    );
    child = BrightnessLayer(
      child: RepaintBoundary(
        key: misc.meRepaintBoundaryKey,
        child: child,
      ),
    );
    if (!Release.sealed || envOverrode) {
      child = _buildEnv(context, child);
    }
    // Constraint the builder as a simulated device.
    // if (_fixedDeviceSize != null && kDebugMode) {
    //   app = Align(
    //     alignment: Alignment.bottomCenter,
    //     child: DecoratedBox(
    //       position: DecorationPosition.foreground,
    //       decoration: BoxDecoration(
    //         border: Border.all(color: ColorName.themeColorDark),
    //       ),
    //       child: MediaQuery(
    //         data: MediaQuery.of(context).copyWith(size: _fixedDeviceSize),
    //         child: ClipRect(
    //           child: ConstrainedBox(
    //             constraints: BoxConstraints.tight(_fixedDeviceSize!),
    //             child: app,
    //           ),
    //         ),
    //       ),
    //     ),
    //   );
    // }
    _configureUIConfig(context);
    return child;
  }

  Widget _buildEnv(BuildContext context, Widget child) {
    final buffer = StringBuffer(envActive.env.toUpperCase());
    if (envOverrode) {
      buffer.write('(O)');
    }
    return Banner(
      message: buffer.toString(),
      location: BannerLocation.bottomStart,
      color: context.themeColor.withValues(alpha: 0.5),
      shadow: BoxShadow(color: context.themeColor.withValues(alpha: 0.5)),
      child: child,
    );
  }
}
