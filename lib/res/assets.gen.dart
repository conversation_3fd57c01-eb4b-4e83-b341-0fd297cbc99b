/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/back.svg
  SvgGenImage get back => const SvgGenImage('assets/icons/back.svg');

  /// File path: assets/icons/beta.svg
  SvgGenImage get beta => const SvgGenImage('assets/icons/beta.svg');

  /// File path: assets/icons/button_copy.svg
  SvgGenImage get buttonCopy => const SvgGenImage('assets/icons/button_copy.svg');

  /// File path: assets/icons/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icons/check.svg');

  /// File path: assets/icons/drag_handle.svg
  SvgGenImage get dragHandle => const SvgGenImage('assets/icons/drag_handle.svg');

  /// File path: assets/icons/header_fun.svg
  SvgGenImage get headerFun => const SvgGenImage('assets/icons/header_fun.svg');

  /// File path: assets/icons/home_card_nfc.svg
  SvgGenImage get homeCardNfc => const SvgGenImage('assets/icons/home_card_nfc.svg');

  /// File path: assets/icons/icon_progress_animating.svg
  SvgGenImage get iconProgressAnimating => const SvgGenImage('assets/icons/icon_progress_animating.svg');

  /// Directory path: assets/icons/images
  $AssetsIconsImagesGen get images => const $AssetsIconsImagesGen();

  /// File path: assets/icons/logo_dark.svg
  SvgGenImage get logoDark => const SvgGenImage('assets/icons/logo_dark.svg');

  /// File path: assets/icons/logo_light.svg
  SvgGenImage get logoLight => const SvgGenImage('assets/icons/logo_light.svg');

  /// File path: assets/icons/logo_text_dark.svg
  SvgGenImage get logoTextDark => const SvgGenImage('assets/icons/logo_text_dark.svg');

  /// File path: assets/icons/logo_text_light.svg
  SvgGenImage get logoTextLight => const SvgGenImage('assets/icons/logo_text_light.svg');

  /// File path: assets/icons/message.svg
  SvgGenImage get message => const SvgGenImage('assets/icons/message.svg');

  /// File path: assets/icons/nav_card.svg
  SvgGenImage get navCard => const SvgGenImage('assets/icons/nav_card.svg');

  /// File path: assets/icons/nav_fun.svg
  SvgGenImage get navFun => const SvgGenImage('assets/icons/nav_fun.svg');

  /// File path: assets/icons/nav_wallet.svg
  SvgGenImage get navWallet => const SvgGenImage('assets/icons/nav_wallet.svg');

  /// File path: assets/icons/placeholder_cat.svg
  SvgGenImage get placeholderCat => const SvgGenImage('assets/icons/placeholder_cat.svg');

  /// File path: assets/icons/proof_of_connection.svg
  SvgGenImage get proofOfConnection => const SvgGenImage('assets/icons/proof_of_connection.svg');

  /// File path: assets/icons/receive.svg
  SvgGenImage get receive => const SvgGenImage('assets/icons/receive.svg');

  /// Directory path: assets/icons/scan
  $AssetsIconsScanGen get scan => const $AssetsIconsScanGen();

  /// File path: assets/icons/send.svg
  SvgGenImage get send => const SvgGenImage('assets/icons/send.svg');

  /// Directory path: assets/icons/setting
  $AssetsIconsSettingGen get setting => const $AssetsIconsSettingGen();

  /// Directory path: assets/icons/social
  $AssetsIconsSocialGen get social => const $AssetsIconsSocialGen();

  /// File path: assets/icons/swap.svg
  SvgGenImage get swap => const SvgGenImage('assets/icons/swap.svg');

  /// File path: assets/icons/verified.svg
  SvgGenImage get verified => const SvgGenImage('assets/icons/verified.svg');

  /// File path: assets/icons/vertical_top.svg
  SvgGenImage get verticalTop => const SvgGenImage('assets/icons/vertical_top.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    back,
    beta,
    buttonCopy,
    check,
    dragHandle,
    headerFun,
    homeCardNfc,
    iconProgressAnimating,
    logoDark,
    logoLight,
    logoTextDark,
    logoTextLight,
    message,
    navCard,
    navFun,
    navWallet,
    placeholderCat,
    proofOfConnection,
    receive,
    send,
    swap,
    verified,
    verticalTop,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/acard.json
  LottieGenImage get acard => const LottieGenImage('assets/lottie/acard.json');

  /// File path: assets/lottie/home.json
  LottieGenImage get home => const LottieGenImage('assets/lottie/home.json');

  /// File path: assets/lottie/loading.json
  LottieGenImage get loading => const LottieGenImage('assets/lottie/loading.json');

  /// File path: assets/lottie/voice.json
  LottieGenImage get voice => const LottieGenImage('assets/lottie/voice.json');

  /// List of all assets
  List<LottieGenImage> get values => [acard, home, loading, voice];
}

class $AssetsMediaGen {
  const $AssetsMediaGen();

  /// File path: assets/media/home.mp4
  String get home => 'assets/media/home.mp4';

  /// File path: assets/media/placeholder_anime.mp4
  String get placeholderAnime => 'assets/media/placeholder_anime.mp4';

  /// File path: assets/media/room_mic_request.mp3
  String get roomMicRequest => 'assets/media/room_mic_request.mp3';

  /// List of all assets
  List<String> get values => [home, placeholderAnime, roomMicRequest];
}

class $AssetsIconsImagesGen {
  const $AssetsIconsImagesGen();

  /// File path: assets/icons/images/ad_1.png
  AssetGenImage get ad1 => const AssetGenImage('assets/icons/images/ad_1.png');

  /// File path: assets/icons/images/ad_2.png
  AssetGenImage get ad2 => const AssetGenImage('assets/icons/images/ad_2.png');

  /// File path: assets/icons/images/ad_3.png
  AssetGenImage get ad3 => const AssetGenImage('assets/icons/images/ad_3.png');

  /// File path: assets/icons/images/banner2.png
  AssetGenImage get banner2 => const AssetGenImage('assets/icons/images/banner2.png');

  /// File path: assets/icons/images/bg-points.png
  AssetGenImage get bgPoints => const AssetGenImage('assets/icons/images/bg-points.png');

  /// File path: assets/icons/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/icons/images/logo.png');

  /// File path: assets/icons/images/metal_backcover.svg
  SvgGenImage get metalBackcover => const SvgGenImage('assets/icons/images/metal_backcover.svg');

  /// File path: assets/icons/images/metal_frontcover.svg
  SvgGenImage get metalFrontcover => const SvgGenImage('assets/icons/images/metal_frontcover.svg');

  /// File path: assets/icons/images/normal_backcover.png
  AssetGenImage get normalBackcover => const AssetGenImage('assets/icons/images/normal_backcover.png');

  /// File path: assets/icons/images/normal_frontcover.png
  AssetGenImage get normalFrontcover => const AssetGenImage('assets/icons/images/normal_frontcover.png');

  /// File path: assets/icons/images/stickerCover.png
  AssetGenImage get stickerCover => const AssetGenImage('assets/icons/images/stickerCover.png');

  /// File path: assets/icons/images/wristbandCover.png
  AssetGenImage get wristbandCover => const AssetGenImage('assets/icons/images/wristbandCover.png');

  /// List of all assets
  List<dynamic> get values => [
    ad1,
    ad2,
    ad3,
    banner2,
    bgPoints,
    logo,
    metalBackcover,
    metalFrontcover,
    normalBackcover,
    normalFrontcover,
    stickerCover,
    wristbandCover,
  ];
}

class $AssetsIconsScanGen {
  const $AssetsIconsScanGen();

  /// File path: assets/icons/scan/flashlight_black.svg
  SvgGenImage get flashlightBlack => const SvgGenImage('assets/icons/scan/flashlight_black.svg');

  /// File path: assets/icons/scan/flashlight_white.svg
  SvgGenImage get flashlightWhite => const SvgGenImage('assets/icons/scan/flashlight_white.svg');

  /// File path: assets/icons/scan/gallery.svg
  SvgGenImage get gallery => const SvgGenImage('assets/icons/scan/gallery.svg');

  /// File path: assets/icons/scan/scanner.svg
  SvgGenImage get scanner => const SvgGenImage('assets/icons/scan/scanner.svg');

  /// File path: assets/icons/scan/warning_yellow.svg
  SvgGenImage get warningYellow => const SvgGenImage('assets/icons/scan/warning_yellow.svg');

  /// List of all assets
  List<SvgGenImage> get values => [flashlightBlack, flashlightWhite, gallery, scanner, warningYellow];
}

class $AssetsIconsSettingGen {
  const $AssetsIconsSettingGen();

  /// File path: assets/icons/setting/about.svg
  SvgGenImage get about => const SvgGenImage('assets/icons/setting/about.svg');

  /// File path: assets/icons/setting/index.svg
  SvgGenImage get index => const SvgGenImage('assets/icons/setting/index.svg');

  /// File path: assets/icons/setting/mode.svg
  SvgGenImage get mode => const SvgGenImage('assets/icons/setting/mode.svg');

  /// File path: assets/icons/setting/person_shield.svg
  SvgGenImage get personShield => const SvgGenImage('assets/icons/setting/person_shield.svg');

  /// List of all assets
  List<SvgGenImage> get values => [about, index, mode, personShield];
}

class $AssetsIconsSocialGen {
  const $AssetsIconsSocialGen();

  /// File path: assets/icons/social/calendly.svg
  SvgGenImage get calendly => const SvgGenImage('assets/icons/social/calendly.svg');

  /// Directory path: assets/icons/social/demo
  $AssetsIconsSocialDemoGen get demo => const $AssetsIconsSocialDemoGen();

  /// File path: assets/icons/social/discord.svg
  SvgGenImage get discord => const SvgGenImage('assets/icons/social/discord.svg');

  /// File path: assets/icons/social/email.svg
  SvgGenImage get email => const SvgGenImage('assets/icons/social/email.svg');

  /// File path: assets/icons/social/farcaster.svg
  SvgGenImage get farcaster => const SvgGenImage('assets/icons/social/farcaster.svg');

  /// File path: assets/icons/social/github.svg
  SvgGenImage get github => const SvgGenImage('assets/icons/social/github.svg');

  /// File path: assets/icons/social/instagram.svg
  SvgGenImage get instagram => const SvgGenImage('assets/icons/social/instagram.svg');

  /// File path: assets/icons/social/linkTree.svg
  SvgGenImage get linkTree => const SvgGenImage('assets/icons/social/linkTree.svg');

  /// File path: assets/icons/social/linkedIn.svg
  SvgGenImage get linkedIn => const SvgGenImage('assets/icons/social/linkedIn.svg');

  /// File path: assets/icons/social/memex.svg
  SvgGenImage get memex => const SvgGenImage('assets/icons/social/memex.svg');

  /// File path: assets/icons/social/ord.svg
  SvgGenImage get ord => const SvgGenImage('assets/icons/social/ord.svg');

  /// File path: assets/icons/social/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/icons/social/phone.svg');

  /// File path: assets/icons/social/telegram.svg
  SvgGenImage get telegram => const SvgGenImage('assets/icons/social/telegram.svg');

  /// File path: assets/icons/social/tiktok.svg
  SvgGenImage get tiktok => const SvgGenImage('assets/icons/social/tiktok.svg');

  /// File path: assets/icons/social/twitter.svg
  SvgGenImage get twitter => const SvgGenImage('assets/icons/social/twitter.svg');

  /// File path: assets/icons/social/whatsapp.svg
  SvgGenImage get whatsapp => const SvgGenImage('assets/icons/social/whatsapp.svg');

  /// File path: assets/icons/social/youtube.svg
  SvgGenImage get youtube => const SvgGenImage('assets/icons/social/youtube.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    calendly,
    discord,
    email,
    farcaster,
    github,
    instagram,
    linkTree,
    linkedIn,
    memex,
    ord,
    phone,
    telegram,
    tiktok,
    twitter,
    whatsapp,
    youtube,
  ];
}

class $AssetsIconsSocialDemoGen {
  const $AssetsIconsSocialDemoGen();

  /// File path: assets/icons/social/demo/Farcaster.png
  AssetGenImage get farcaster => const AssetGenImage('assets/icons/social/demo/Farcaster.png');

  /// File path: assets/icons/social/demo/GitHub.png
  AssetGenImage get gitHub => const AssetGenImage('assets/icons/social/demo/GitHub.png');

  /// File path: assets/icons/social/demo/Instagram.png
  AssetGenImage get instagram => const AssetGenImage('assets/icons/social/demo/Instagram.png');

  /// File path: assets/icons/social/demo/LinkedIn.png
  AssetGenImage get linkedIn => const AssetGenImage('assets/icons/social/demo/LinkedIn.png');

  /// File path: assets/icons/social/demo/Linktree.png
  AssetGenImage get linktree => const AssetGenImage('assets/icons/social/demo/Linktree.png');

  /// File path: assets/icons/social/demo/MemeX.png
  AssetGenImage get memeX => const AssetGenImage('assets/icons/social/demo/MemeX.png');

  /// File path: assets/icons/social/demo/Telegram.png
  AssetGenImage get telegram => const AssetGenImage('assets/icons/social/demo/Telegram.png');

  /// File path: assets/icons/social/demo/TikTok.png
  AssetGenImage get tikTok => const AssetGenImage('assets/icons/social/demo/TikTok.png');

  /// File path: assets/icons/social/demo/X-Twitter.png
  AssetGenImage get xTwitter => const AssetGenImage('assets/icons/social/demo/X-Twitter.png');

  /// File path: assets/icons/social/demo/YouTube.png
  AssetGenImage get youTube => const AssetGenImage('assets/icons/social/demo/YouTube.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    farcaster,
    gitHub,
    instagram,
    linkedIn,
    linktree,
    memeX,
    telegram,
    tikTok,
    xTwitter,
    youTube,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
  static const $AssetsMediaGen media = $AssetsMediaGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(_assetName, assetBundle: bundle, packageName: package, theme: theme);
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
