// ignore_for_file: directives_ordering

export 'dart:async' show FutureOr, TimeoutException, Timer;
export 'dart:convert' show Utf8Codec, jsonDecode, jsonEncode, base64, base64Url, utf8;

////////////////////////////////////////////////////////////////////////////////
export 'package:auto_size_text/auto_size_text.dart';
export 'package:collection/collection.dart';
export 'package:convert/convert.dart' show HexCodec, hex;
export 'package:decimal/decimal.dart';
export 'package:dio/dio.dart' show CancelToken, DioException, DioExceptionType;
export 'package:ff_annotation_route_library/ff_annotation_route_library.dart';
export 'package:flutter/services.dart' show HapticFeedback, TextInputFormatter;
export 'package:flutter_riverpod/flutter_riverpod.dart';
export 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
export 'package:flutter_svg/flutter_svg.dart' show SvgPicture;
export 'package:hive_ce_flutter/hive_flutter.dart';
export 'package:intl/intl.dart' show DateFormat;
export 'package:json_annotation/json_annotation.dart';
export 'package:lottie/lottie.dart' show Lottie;
export 'package:privy_flutter/privy_flutter.dart';

////////////////////////////////////////////////////////////////////////////////
export 'package:me_constants/me_constants.dart';
export 'package:me_extensions/me_extensions.dart';
export 'package:me_fonts/me_fonts.dart';
export 'package:me_l10n/me_l10n.dart';
export 'package:me_misc/me_misc.dart'
    show
        CacheImageProvider,
        CachedSvgNetworkBytesLoader,
        MENavigatorExtension,
        MERetryExtension,
        MERetryFunctionExtension,
        ScalableImageCacheHttpSource,
        hideKeyboard,
        meContext,
        meNavigator,
        postRun,
        retryWith;
export 'package:me_ui/me_ui.dart' hide MELoading;
export 'package:me_utils/me_utils.dart';

////////////////////////////////////////////////////////////////////////////////
export 'package:permission_handler/permission_handler.dart';
export 'package:rational/rational.dart' show Rational;
export 'package:solana/solana.dart' show Ed25519HDPublicKey;
export 'package:url_launcher/url_launcher.dart' show LaunchMode;
export 'package:url_launcher/url_launcher_string.dart' show canLaunchUrlString, launchUrlString;

////////////////////////////////////////////////////////////////////////////////
export 'constants/constants.dart';
export 'constants/envs.dart';
export 'constants/release.dart';

////////////////////////////////////////////////////////////////////////////////
export 'extensions/build_context_extension.dart';
export 'extensions/riverpod_extension.dart';
export 'extensions/solana_extension.dart';

////////////////////////////////////////////////////////////////////////////////
export 'feat/scan/handler.dart';
export 'feat/scan/uni_qr.dart' show QR, ScanManager;
export 'feat/push/helper.dart';

////////////////////////////////////////////////////////////////////////////////
export 'internals/box.dart' hide Boxes;
export 'internals/methods.dart' show handleExceptions;
export 'internals/privy.dart';
export 'internals/riverpod.dart';

////////////////////////////////////////////////////////////////////////////////
export 'l10n/gen/app_localizations.dart';

////////////////////////////////////////////////////////////////////////////////
export 'provider/api.dart' show ApiException, Rep, ListRep, Paged;
// export 'provider/user.dart' show persistentTokenRepoProvider, userRepoProvider;

////////////////////////////////////////////////////////////////////////////////
export 'res/assets.gen.dart';
export 'res/colors.gen.dart';
export 'res/fonts.gen.dart';

////////////////////////////////////////////////////////////////////////////////
export 'routes/card3_route.dart' show getRouteSettings;
export 'routes/card3_routes.dart';

////////////////////////////////////////////////////////////////////////////////
export 'ui/widgets/additional_counter.dart';
export 'ui/widgets/animated_text.dart';
export 'ui/widgets/app_back_button.dart';
export 'ui/widgets/app_loading.dart';
export 'ui/widgets/app_scaffold.dart';
export 'ui/widgets/change_text.dart';
export 'ui/widgets/copy_button.dart';
export 'ui/widgets/empty_view.dart';
export 'ui/widgets/github_contribution_graph.dart';
export 'ui/widgets/progressing_bar.dart';
export 'ui/widgets/toast.dart';
