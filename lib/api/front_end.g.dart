// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'front_end.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_FrontEndGitHubContributionCollection
_$FrontEndGitHubContributionCollectionFromJson(Map json) =>
    _FrontEndGitHubContributionCollection(
      calendar: FrontEndGitHubContributionCalendar.fromJson(
        Map<String, dynamic>.from(json['contributionCalendar'] as Map),
      ),
    );

Map<String, dynamic> _$FrontEndGitHubContributionCollectionToJson(
  _FrontEndGitHubContributionCollection instance,
) => <String, dynamic>{'contributionCalendar': instance.calendar.toJson()};

_FrontEndGitHubContributionCalendar
_$FrontEndGitHubContributionCalendarFromJson(Map json) =>
    _FrontEndGitHubContributionCalendar(
      total: (json['totalContributions'] as num).toInt(),
      weeks: (json['weeks'] as List<dynamic>)
          .map(
            (e) => FrontEndGitHubContributionDays.fromJson(
              Map<String, dynamic>.from(e as Map),
            ),
          )
          .toList(),
    );

Map<String, dynamic> _$FrontEndGitHubContributionCalendarToJson(
  _FrontEndGitHubContributionCalendar instance,
) => <String, dynamic>{
  'totalContributions': instance.total,
  'weeks': instance.weeks.map((e) => e.toJson()).toList(),
};

_FrontEndGitHubContributionDays _$FrontEndGitHubContributionDaysFromJson(
  Map json,
) => _FrontEndGitHubContributionDays(
  days: (json['contributionDays'] as List<dynamic>)
      .map(
        (e) => FrontEndGitHubContributionDay.fromJson(
          Map<String, dynamic>.from(e as Map),
        ),
      )
      .toList(),
);

Map<String, dynamic> _$FrontEndGitHubContributionDaysToJson(
  _FrontEndGitHubContributionDays instance,
) => <String, dynamic>{
  'contributionDays': instance.days.map((e) => e.toJson()).toList(),
};

_FrontEndGitHubContributionDay _$FrontEndGitHubContributionDayFromJson(
  Map json,
) => _FrontEndGitHubContributionDay(
  contributionCount: (json['contributionCount'] as num).toInt(),
  rawDate: json['date'] as String,
);

Map<String, dynamic> _$FrontEndGitHubContributionDayToJson(
  _FrontEndGitHubContributionDay instance,
) => <String, dynamic>{
  'contributionCount': instance.contributionCount,
  'date': instance.rawDate,
};
