// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Social _$SocialFromJson(Map json) => _Social(
  handleName: json['handleName'] as String,
  id: (json['id'] as num).toInt(),
  platformName: json['platformName'] as String,
  platformUrl: json['platformUrl'] as String,
  isVerify: json['isVerify'] as bool? ?? false,
);

Map<String, dynamic> _$SocialToJson(_Social instance) => <String, dynamic>{
  'handleName': instance.handleName,
  'id': instance.id,
  'platformName': instance.platformName,
  'platformUrl': instance.platformUrl,
  'isVerify': instance.isVerify,
};

_UserInfo _$UserInfoFromJson(Map json) => _UserInfo(
  avatar: json['avatar'] as String? ?? '',
  bannerHref: json['bannerHref'] as String? ?? '',
  bannerImage: json['bannerImage'] as String? ?? '',
  cardCode: json['cardCode'] as String? ?? '',
  company: json['company'] as String? ?? '',
  currentType: (json['currentType'] as num?)?.toInt() ?? 0,
  evmWallet: json['evmWallet'] as String? ?? '',
  handle: json['handle'] as String? ?? '',
  integral: (json['integral'] as num?)?.toInt() ?? 0,
  lastMessageId: (json['lastMessageId'] as num?)?.toInt() ?? 0,
  latestMessageId: (json['latestMessageId'] as num?)?.toInt() ?? 0,
  name: json['name'] as String? ?? '',
  profileMode:
      $enumDecodeNullable(_$ProfileModeEnumMap, json['profileMode']) ??
      ProfileMode.EMPTY,
  redirectUrl: json['redirectUrl'] as String? ?? '',
  referralCode: json['referralCode'] as String? ?? '',
  title: json['title'] as String? ?? '',
  userEmail: json['userEmail'] as String? ?? '',
);

Map<String, dynamic> _$UserInfoToJson(_UserInfo instance) => <String, dynamic>{
  'avatar': instance.avatar,
  'bannerHref': instance.bannerHref,
  'bannerImage': instance.bannerImage,
  'cardCode': instance.cardCode,
  'company': instance.company,
  'currentType': instance.currentType,
  'evmWallet': instance.evmWallet,
  'handle': instance.handle,
  'integral': instance.integral,
  'lastMessageId': instance.lastMessageId,
  'latestMessageId': instance.latestMessageId,
  'name': instance.name,
  'profileMode': _$ProfileModeEnumMap[instance.profileMode]!,
  'redirectUrl': instance.redirectUrl,
  'referralCode': instance.referralCode,
  'title': instance.title,
  'userEmail': instance.userEmail,
};

const _$ProfileModeEnumMap = {
  ProfileMode.DEFAULT: 'DEFAULT',
  ProfileMode.ETHCC: 'ETHCC',
  ProfileMode.EMPTY: '',
};

_CardInfo _$CardInfoFromJson(Map json) => _CardInfo(
  active: json['active'] as bool,
  activeTime: json['activeTime'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  card3EventId: (json['card3EventId'] as num?)?.toInt() ?? 0,
  cardCode: json['cardCode'] as String? ?? '',
  cardType:
      $enumDecodeNullable(_$CardTypeEnumMap, json['cardType']) ?? CardType.CARD,
  chainId: (json['chainId'] as num?)?.toInt() ?? 0,
  eventName: json['eventName'] as String? ?? '',
  id: (json['id'] as num?)?.toInt() ?? 0,
  isActive: json['isActive'] as bool? ?? false,
  referralCode: json['referralCode'] as String? ?? '',
  virtualCard: json['virtualCard'] as bool? ?? false,
  nfcType:
      $enumDecodeNullable(_$NfcTypeEnumMap, json['nfcType']) ?? NfcType.NFC215,
);

Map<String, dynamic> _$CardInfoToJson(_CardInfo instance) => <String, dynamic>{
  'active': instance.active,
  'activeTime': instance.activeTime,
  'backCover': instance.backCover,
  'card3EventId': instance.card3EventId,
  'cardCode': instance.cardCode,
  'cardType': _$CardTypeEnumMap[instance.cardType]!,
  'chainId': instance.chainId,
  'eventName': instance.eventName,
  'id': instance.id,
  'isActive': instance.isActive,
  'referralCode': instance.referralCode,
  'virtualCard': instance.virtualCard,
  'nfcType': _$NfcTypeEnumMap[instance.nfcType]!,
};

const _$CardTypeEnumMap = {
  CardType.STICKER: 'STICKER',
  CardType.CARD: 'CARD',
  CardType.WRISTBAND: 'WRISTBAND',
};

const _$NfcTypeEnumMap = {NfcType.NFC215: 'NFC215', NfcType.NFC424: 'NFC424'};

_CoverInfo _$CoverInfoFromJson(Map json) => _CoverInfo(
  activeMode: json['activeMode'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  eventId: json['eventId'] as String? ?? '',
  eventName: json['eventName'] as String? ?? '',
  paymentLink: json['paymentLink'] as String? ?? '',
  price: (json['price'] as num?)?.toInt() ?? 0,
  priceDescription: json['priceDescription'] as String? ?? '',
  printType:
      $enumDecodeNullable(_$PrintTypeEnumMap, json['printType']) ??
      PrintType.NORMAL,
  thirdPartyLink: json['thirdPartyLink'] as String? ?? '',
);

Map<String, dynamic> _$CoverInfoToJson(_CoverInfo instance) =>
    <String, dynamic>{
      'activeMode': instance.activeMode,
      'backCover': instance.backCover,
      'eventId': instance.eventId,
      'eventName': instance.eventName,
      'paymentLink': instance.paymentLink,
      'price': instance.price,
      'priceDescription': instance.priceDescription,
      'printType': _$PrintTypeEnumMap[instance.printType]!,
      'thirdPartyLink': instance.thirdPartyLink,
    };

const _$PrintTypeEnumMap = {
  PrintType.METAL: 'METAL',
  PrintType.NORMAL: 'NORMAL',
};

_CreateCardCoverResponse _$CreateCardCoverResponseFromJson(Map json) =>
    _CreateCardCoverResponse(
      code: json['code'] as String,
      paymentLink: json['paymentLink'] as String? ?? '',
    );

Map<String, dynamic> _$CreateCardCoverResponseToJson(
  _CreateCardCoverResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'paymentLink': instance.paymentLink,
};

_Group _$GroupFromJson(Map json) => _Group(
  id: json['uniqId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  logo: json['logo'] as String? ?? '',
  userCount: (json['userCount'] as num?)?.toInt() ?? 1,
  creatorName: json['creator'] as String,
);

Map<String, dynamic> _$GroupToJson(_Group instance) => <String, dynamic>{
  'uniqId': instance.id,
  'name': instance.name,
  'description': instance.description,
  'logo': instance.logo,
  'userCount': instance.userCount,
  'creator': instance.creatorName,
};

_EthccProfile _$EthccProfileFromJson(Map json) => _EthccProfile(
  githubHandle: json['githubHandle'] as String? ?? '',
  topics: json['topics'] == null
      ? const []
      : EthccProfile.fromJoinedString(json['topics'] as String),
  roles: json['role'] == null
      ? const []
      : EthccProfile.fromJoinedString(json['role'] as String),
);

Map<String, dynamic> _$EthccProfileToJson(_EthccProfile instance) =>
    <String, dynamic>{
      'githubHandle': instance.githubHandle,
      'topics': EthccProfile.toJoinedString(instance.topics),
      'role': EthccProfile.toJoinedString(instance.roles),
    };

_UpdateEthccTopicsRequest _$UpdateEthccTopicsRequestFromJson(Map json) =>
    _UpdateEthccTopicsRequest(topics: json['topics'] as String);

Map<String, dynamic> _$UpdateEthccTopicsRequestToJson(
  _UpdateEthccTopicsRequest instance,
) => <String, dynamic>{'topics': instance.topics};

_UpdateEthccGithubRequest _$UpdateEthccGithubRequestFromJson(Map json) =>
    _UpdateEthccGithubRequest(githubHandle: json['githubHandle'] as String);

Map<String, dynamic> _$UpdateEthccGithubRequestToJson(
  _UpdateEthccGithubRequest instance,
) => <String, dynamic>{'githubHandle': instance.githubHandle};

_UserSettings _$UserSettingsFromJson(Map json) => _UserSettings(
  fcmAndroid: json['fcmAndroid'] as String? ?? '',
  fcmIos: json['fcmIos'] as String? ?? '',
  fcmWeb: json['fcmWeb'] as String? ?? '',
);

Map<String, dynamic> _$UserSettingsToJson(_UserSettings instance) =>
    <String, dynamic>{
      'fcmAndroid': instance.fcmAndroid,
      'fcmIos': instance.fcmIos,
      'fcmWeb': instance.fcmWeb,
    };
