// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Social {
  @Json<PERSON>ey(name: 'handleName')
  String get handleName;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'platformName')
  String get platformName;
  @JsonKey(name: 'platformUrl')
  String get platformUrl;
  @JsonKey(name: 'isVerify')
  bool get isVerify;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SocialCopyWith<Social> get copyWith =>
      _$SocialCopyWithImpl<Social>(this as Social, _$identity);

  /// Serializes this Social to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Social &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.isVerify, isVerify) ||
                other.isVerify == isVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    handleName,
    id,
    platformName,
    platformUrl,
    isVerify,
  );

  @override
  String toString() {
    return 'Social(handleName: $handleName, id: $id, platformName: $platformName, platformUrl: $platformUrl, isVerify: $isVerify)';
  }
}

/// @nodoc
abstract mixin class $SocialCopyWith<$Res> {
  factory $SocialCopyWith(Social value, $Res Function(Social) _then) =
      _$SocialCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'isVerify') bool isVerify,
  });
}

/// @nodoc
class _$SocialCopyWithImpl<$Res> implements $SocialCopyWith<$Res> {
  _$SocialCopyWithImpl(this._self, this._then);

  final Social _self;
  final $Res Function(Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? handleName = null,
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? isVerify = null,
  }) {
    return _then(
      _self.copyWith(
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerify: null == isVerify
            ? _self.isVerify
            : isVerify // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Social implements Social {
  const _Social({
    @JsonKey(name: 'handleName') required this.handleName,
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'platformName') required this.platformName,
    @JsonKey(name: 'platformUrl') required this.platformUrl,
    @JsonKey(name: 'isVerify') this.isVerify = false,
  });
  factory _Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);

  @override
  @JsonKey(name: 'handleName')
  final String handleName;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'platformName')
  final String platformName;
  @override
  @JsonKey(name: 'platformUrl')
  final String platformUrl;
  @override
  @JsonKey(name: 'isVerify')
  final bool isVerify;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SocialCopyWith<_Social> get copyWith =>
      __$SocialCopyWithImpl<_Social>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SocialToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Social &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.isVerify, isVerify) ||
                other.isVerify == isVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    handleName,
    id,
    platformName,
    platformUrl,
    isVerify,
  );

  @override
  String toString() {
    return 'Social(handleName: $handleName, id: $id, platformName: $platformName, platformUrl: $platformUrl, isVerify: $isVerify)';
  }
}

/// @nodoc
abstract mixin class _$SocialCopyWith<$Res> implements $SocialCopyWith<$Res> {
  factory _$SocialCopyWith(_Social value, $Res Function(_Social) _then) =
      __$SocialCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'isVerify') bool isVerify,
  });
}

/// @nodoc
class __$SocialCopyWithImpl<$Res> implements _$SocialCopyWith<$Res> {
  __$SocialCopyWithImpl(this._self, this._then);

  final _Social _self;
  final $Res Function(_Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? handleName = null,
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? isVerify = null,
  }) {
    return _then(
      _Social(
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerify: null == isVerify
            ? _self.isVerify
            : isVerify // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$UserInfo {
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'bannerHref')
  String get bannerHref;
  @JsonKey(name: 'bannerImage')
  String get bannerImage;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'company')
  String get company;
  @JsonKey(name: 'currentType')
  int get currentType;
  @JsonKey(name: 'evmWallet')
  String get evmWallet;
  @JsonKey(name: 'handle')
  String get handle;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'lastMessageId')
  int get lastMessageId;
  @JsonKey(name: 'latestMessageId')
  int get latestMessageId;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'profileMode')
  ProfileMode get profileMode;
  @JsonKey(name: 'redirectUrl')
  String get redirectUrl;
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'userEmail')
  String get userEmail;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<UserInfo> get copyWith =>
      _$UserInfoCopyWithImpl<UserInfo>(this as UserInfo, _$identity);

  /// Serializes this UserInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserInfo &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.bannerHref, bannerHref) ||
                other.bannerHref == bannerHref) &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.currentType, currentType) ||
                other.currentType == currentType) &&
            (identical(other.evmWallet, evmWallet) ||
                other.evmWallet == evmWallet) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    avatar,
    bannerHref,
    bannerImage,
    cardCode,
    company,
    currentType,
    evmWallet,
    handle,
    integral,
    lastMessageId,
    latestMessageId,
    name,
    profileMode,
    redirectUrl,
    referralCode,
    title,
    userEmail,
  );

  @override
  String toString() {
    return 'UserInfo(avatar: $avatar, bannerHref: $bannerHref, bannerImage: $bannerImage, cardCode: $cardCode, company: $company, currentType: $currentType, evmWallet: $evmWallet, handle: $handle, integral: $integral, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, name: $name, profileMode: $profileMode, redirectUrl: $redirectUrl, referralCode: $referralCode, title: $title, userEmail: $userEmail)';
  }
}

/// @nodoc
abstract mixin class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) _then) =
      _$UserInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'bannerHref') String bannerHref,
    @JsonKey(name: 'bannerImage') String bannerImage,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'currentType') int currentType,
    @JsonKey(name: 'evmWallet') String evmWallet,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'profileMode') ProfileMode profileMode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'userEmail') String userEmail,
  });
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res> implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._self, this._then);

  final UserInfo _self;
  final $Res Function(UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? avatar = null,
    Object? bannerHref = null,
    Object? bannerImage = null,
    Object? cardCode = null,
    Object? company = null,
    Object? currentType = null,
    Object? evmWallet = null,
    Object? handle = null,
    Object? integral = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? name = null,
    Object? profileMode = null,
    Object? redirectUrl = null,
    Object? referralCode = null,
    Object? title = null,
    Object? userEmail = null,
  }) {
    return _then(
      _self.copyWith(
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerHref: null == bannerHref
            ? _self.bannerHref
            : bannerHref // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerImage: null == bannerImage
            ? _self.bannerImage
            : bannerImage // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        currentType: null == currentType
            ? _self.currentType
            : currentType // ignore: cast_nullable_to_non_nullable
                  as int,
        evmWallet: null == evmWallet
            ? _self.evmWallet
            : evmWallet // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profileMode: null == profileMode
            ? _self.profileMode
            : profileMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserInfo implements UserInfo {
  const _UserInfo({
    @JsonKey(name: 'avatar') this.avatar = '',
    @JsonKey(name: 'bannerHref') this.bannerHref = '',
    @JsonKey(name: 'bannerImage') this.bannerImage = '',
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'company') this.company = '',
    @JsonKey(name: 'currentType') this.currentType = 0,
    @JsonKey(name: 'evmWallet') this.evmWallet = '',
    @JsonKey(name: 'handle') this.handle = '',
    @JsonKey(name: 'integral') this.integral = 0,
    @JsonKey(name: 'lastMessageId') this.lastMessageId = 0,
    @JsonKey(name: 'latestMessageId') this.latestMessageId = 0,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'profileMode') this.profileMode = ProfileMode.EMPTY,
    @JsonKey(name: 'redirectUrl') this.redirectUrl = '',
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'userEmail') this.userEmail = '',
  });
  factory _UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  @override
  @JsonKey(name: 'avatar')
  final String avatar;
  @override
  @JsonKey(name: 'bannerHref')
  final String bannerHref;
  @override
  @JsonKey(name: 'bannerImage')
  final String bannerImage;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'company')
  final String company;
  @override
  @JsonKey(name: 'currentType')
  final int currentType;
  @override
  @JsonKey(name: 'evmWallet')
  final String evmWallet;
  @override
  @JsonKey(name: 'handle')
  final String handle;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'lastMessageId')
  final int lastMessageId;
  @override
  @JsonKey(name: 'latestMessageId')
  final int latestMessageId;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'profileMode')
  final ProfileMode profileMode;
  @override
  @JsonKey(name: 'redirectUrl')
  final String redirectUrl;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'userEmail')
  final String userEmail;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserInfoCopyWith<_UserInfo> get copyWith =>
      __$UserInfoCopyWithImpl<_UserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserInfo &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.bannerHref, bannerHref) ||
                other.bannerHref == bannerHref) &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.currentType, currentType) ||
                other.currentType == currentType) &&
            (identical(other.evmWallet, evmWallet) ||
                other.evmWallet == evmWallet) &&
            (identical(other.handle, handle) || other.handle == handle) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.lastMessageId, lastMessageId) ||
                other.lastMessageId == lastMessageId) &&
            (identical(other.latestMessageId, latestMessageId) ||
                other.latestMessageId == latestMessageId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    avatar,
    bannerHref,
    bannerImage,
    cardCode,
    company,
    currentType,
    evmWallet,
    handle,
    integral,
    lastMessageId,
    latestMessageId,
    name,
    profileMode,
    redirectUrl,
    referralCode,
    title,
    userEmail,
  );

  @override
  String toString() {
    return 'UserInfo(avatar: $avatar, bannerHref: $bannerHref, bannerImage: $bannerImage, cardCode: $cardCode, company: $company, currentType: $currentType, evmWallet: $evmWallet, handle: $handle, integral: $integral, lastMessageId: $lastMessageId, latestMessageId: $latestMessageId, name: $name, profileMode: $profileMode, redirectUrl: $redirectUrl, referralCode: $referralCode, title: $title, userEmail: $userEmail)';
  }
}

/// @nodoc
abstract mixin class _$UserInfoCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$UserInfoCopyWith(_UserInfo value, $Res Function(_UserInfo) _then) =
      __$UserInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'bannerHref') String bannerHref,
    @JsonKey(name: 'bannerImage') String bannerImage,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'company') String company,
    @JsonKey(name: 'currentType') int currentType,
    @JsonKey(name: 'evmWallet') String evmWallet,
    @JsonKey(name: 'handle') String handle,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'lastMessageId') int lastMessageId,
    @JsonKey(name: 'latestMessageId') int latestMessageId,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'profileMode') ProfileMode profileMode,
    @JsonKey(name: 'redirectUrl') String redirectUrl,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'userEmail') String userEmail,
  });
}

/// @nodoc
class __$UserInfoCopyWithImpl<$Res> implements _$UserInfoCopyWith<$Res> {
  __$UserInfoCopyWithImpl(this._self, this._then);

  final _UserInfo _self;
  final $Res Function(_UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? avatar = null,
    Object? bannerHref = null,
    Object? bannerImage = null,
    Object? cardCode = null,
    Object? company = null,
    Object? currentType = null,
    Object? evmWallet = null,
    Object? handle = null,
    Object? integral = null,
    Object? lastMessageId = null,
    Object? latestMessageId = null,
    Object? name = null,
    Object? profileMode = null,
    Object? redirectUrl = null,
    Object? referralCode = null,
    Object? title = null,
    Object? userEmail = null,
  }) {
    return _then(
      _UserInfo(
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerHref: null == bannerHref
            ? _self.bannerHref
            : bannerHref // ignore: cast_nullable_to_non_nullable
                  as String,
        bannerImage: null == bannerImage
            ? _self.bannerImage
            : bannerImage // ignore: cast_nullable_to_non_nullable
                  as String,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        company: null == company
            ? _self.company
            : company // ignore: cast_nullable_to_non_nullable
                  as String,
        currentType: null == currentType
            ? _self.currentType
            : currentType // ignore: cast_nullable_to_non_nullable
                  as int,
        evmWallet: null == evmWallet
            ? _self.evmWallet
            : evmWallet // ignore: cast_nullable_to_non_nullable
                  as String,
        handle: null == handle
            ? _self.handle
            : handle // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        lastMessageId: null == lastMessageId
            ? _self.lastMessageId
            : lastMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        latestMessageId: null == latestMessageId
            ? _self.latestMessageId
            : latestMessageId // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        profileMode: null == profileMode
            ? _self.profileMode
            : profileMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        redirectUrl: null == redirectUrl
            ? _self.redirectUrl
            : redirectUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        userEmail: null == userEmail
            ? _self.userEmail
            : userEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$CardInfo {
  @JsonKey(name: 'active')
  bool get active;
  @JsonKey(name: 'activeTime')
  String get activeTime;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'card3EventId')
  int get card3EventId;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'cardType')
  CardType get cardType;
  @JsonKey(name: 'chainId')
  int get chainId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'isActive')
  bool get isActive;
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'virtualCard')
  bool get virtualCard;
  @JsonKey(name: 'nfcType')
  NfcType get nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoCopyWith<CardInfo> get copyWith =>
      _$CardInfoCopyWithImpl<CardInfo>(this as CardInfo, _$identity);

  /// Serializes this CardInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfo &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.card3EventId, card3EventId) ||
                other.card3EventId == card3EventId) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    active,
    activeTime,
    backCover,
    card3EventId,
    cardCode,
    cardType,
    chainId,
    eventName,
    id,
    isActive,
    referralCode,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(active: $active, activeTime: $activeTime, backCover: $backCover, card3EventId: $card3EventId, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventName: $eventName, id: $id, isActive: $isActive, referralCode: $referralCode, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class $CardInfoCopyWith<$Res> {
  factory $CardInfoCopyWith(CardInfo value, $Res Function(CardInfo) _then) =
      _$CardInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'active') bool active,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'card3EventId') int card3EventId,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class _$CardInfoCopyWithImpl<$Res> implements $CardInfoCopyWith<$Res> {
  _$CardInfoCopyWithImpl(this._self, this._then);

  final CardInfo _self;
  final $Res Function(CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? active = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? card3EventId = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? referralCode = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _self.copyWith(
        active: null == active
            ? _self.active
            : active // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        card3EventId: null == card3EventId
            ? _self.card3EventId
            : card3EventId // ignore: cast_nullable_to_non_nullable
                  as int,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CardInfo implements CardInfo {
  const _CardInfo({
    @JsonKey(name: 'active') required this.active,
    @JsonKey(name: 'activeTime') this.activeTime = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'card3EventId') this.card3EventId = 0,
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'cardType') this.cardType = CardType.CARD,
    @JsonKey(name: 'chainId') this.chainId = 0,
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'id') this.id = 0,
    @JsonKey(name: 'isActive') this.isActive = false,
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'virtualCard') this.virtualCard = false,
    @JsonKey(name: 'nfcType') this.nfcType = NfcType.NFC215,
  });
  factory _CardInfo.fromJson(Map<String, dynamic> json) =>
      _$CardInfoFromJson(json);

  @override
  @JsonKey(name: 'active')
  final bool active;
  @override
  @JsonKey(name: 'activeTime')
  final String activeTime;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'card3EventId')
  final int card3EventId;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'cardType')
  final CardType cardType;
  @override
  @JsonKey(name: 'chainId')
  final int chainId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isActive')
  final bool isActive;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'virtualCard')
  final bool virtualCard;
  @override
  @JsonKey(name: 'nfcType')
  final NfcType nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardInfoCopyWith<_CardInfo> get copyWith =>
      __$CardInfoCopyWithImpl<_CardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardInfo &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.card3EventId, card3EventId) ||
                other.card3EventId == card3EventId) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    active,
    activeTime,
    backCover,
    card3EventId,
    cardCode,
    cardType,
    chainId,
    eventName,
    id,
    isActive,
    referralCode,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(active: $active, activeTime: $activeTime, backCover: $backCover, card3EventId: $card3EventId, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventName: $eventName, id: $id, isActive: $isActive, referralCode: $referralCode, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class _$CardInfoCopyWith<$Res>
    implements $CardInfoCopyWith<$Res> {
  factory _$CardInfoCopyWith(_CardInfo value, $Res Function(_CardInfo) _then) =
      __$CardInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'active') bool active,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'card3EventId') int card3EventId,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class __$CardInfoCopyWithImpl<$Res> implements _$CardInfoCopyWith<$Res> {
  __$CardInfoCopyWithImpl(this._self, this._then);

  final _CardInfo _self;
  final $Res Function(_CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? active = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? card3EventId = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? referralCode = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _CardInfo(
        active: null == active
            ? _self.active
            : active // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        card3EventId: null == card3EventId
            ? _self.card3EventId
            : card3EventId // ignore: cast_nullable_to_non_nullable
                  as int,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// @nodoc
mixin _$CoverInfo {
  @JsonKey(name: 'activeMode')
  String get activeMode;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'eventId')
  String get eventId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;
  @JsonKey(name: 'price')
  int get price;
  @JsonKey(name: 'priceDescription')
  String get priceDescription;
  @JsonKey(name: 'printType')
  PrintType get printType;
  @JsonKey(name: 'thirdPartyLink')
  String get thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CoverInfoCopyWith<CoverInfo> get copyWith =>
      _$CoverInfoCopyWithImpl<CoverInfo>(this as CoverInfo, _$identity);

  /// Serializes this CoverInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class $CoverInfoCopyWith<$Res> {
  factory $CoverInfoCopyWith(CoverInfo value, $Res Function(CoverInfo) _then) =
      _$CoverInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class _$CoverInfoCopyWithImpl<$Res> implements $CoverInfoCopyWith<$Res> {
  _$CoverInfoCopyWithImpl(this._self, this._then);

  final CoverInfo _self;
  final $Res Function(CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _self.copyWith(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CoverInfo implements CoverInfo {
  const _CoverInfo({
    @JsonKey(name: 'activeMode') this.activeMode = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'eventId') this.eventId = '',
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
    @JsonKey(name: 'price') this.price = 0,
    @JsonKey(name: 'priceDescription') this.priceDescription = '',
    @JsonKey(name: 'printType') this.printType = PrintType.NORMAL,
    @JsonKey(name: 'thirdPartyLink') this.thirdPartyLink = '',
  });
  factory _CoverInfo.fromJson(Map<String, dynamic> json) =>
      _$CoverInfoFromJson(json);

  @override
  @JsonKey(name: 'activeMode')
  final String activeMode;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'eventId')
  final String eventId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;
  @override
  @JsonKey(name: 'price')
  final int price;
  @override
  @JsonKey(name: 'priceDescription')
  final String priceDescription;
  @override
  @JsonKey(name: 'printType')
  final PrintType printType;
  @override
  @JsonKey(name: 'thirdPartyLink')
  final String thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CoverInfoCopyWith<_CoverInfo> get copyWith =>
      __$CoverInfoCopyWithImpl<_CoverInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CoverInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class _$CoverInfoCopyWith<$Res>
    implements $CoverInfoCopyWith<$Res> {
  factory _$CoverInfoCopyWith(
    _CoverInfo value,
    $Res Function(_CoverInfo) _then,
  ) = __$CoverInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class __$CoverInfoCopyWithImpl<$Res> implements _$CoverInfoCopyWith<$Res> {
  __$CoverInfoCopyWithImpl(this._self, this._then);

  final _CoverInfo _self;
  final $Res Function(_CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _CoverInfo(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$CreateCardCoverResponse {
  @JsonKey(name: 'code')
  String get code;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateCardCoverResponseCopyWith<CreateCardCoverResponse> get copyWith =>
      _$CreateCardCoverResponseCopyWithImpl<CreateCardCoverResponse>(
        this as CreateCardCoverResponse,
        _$identity,
      );

  /// Serializes this CreateCardCoverResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateCardCoverResponse &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, paymentLink);

  @override
  String toString() {
    return 'CreateCardCoverResponse(code: $code, paymentLink: $paymentLink)';
  }
}

/// @nodoc
abstract mixin class $CreateCardCoverResponseCopyWith<$Res> {
  factory $CreateCardCoverResponseCopyWith(
    CreateCardCoverResponse value,
    $Res Function(CreateCardCoverResponse) _then,
  ) = _$CreateCardCoverResponseCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'paymentLink') String paymentLink,
  });
}

/// @nodoc
class _$CreateCardCoverResponseCopyWithImpl<$Res>
    implements $CreateCardCoverResponseCopyWith<$Res> {
  _$CreateCardCoverResponseCopyWithImpl(this._self, this._then);

  final CreateCardCoverResponse _self;
  final $Res Function(CreateCardCoverResponse) _then;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? code = null, Object? paymentLink = null}) {
    return _then(
      _self.copyWith(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CreateCardCoverResponse implements CreateCardCoverResponse {
  const _CreateCardCoverResponse({
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
  });
  factory _CreateCardCoverResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateCardCoverResponseFromJson(json);

  @override
  @JsonKey(name: 'code')
  final String code;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateCardCoverResponseCopyWith<_CreateCardCoverResponse> get copyWith =>
      __$CreateCardCoverResponseCopyWithImpl<_CreateCardCoverResponse>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CreateCardCoverResponseToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateCardCoverResponse &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, paymentLink);

  @override
  String toString() {
    return 'CreateCardCoverResponse(code: $code, paymentLink: $paymentLink)';
  }
}

/// @nodoc
abstract mixin class _$CreateCardCoverResponseCopyWith<$Res>
    implements $CreateCardCoverResponseCopyWith<$Res> {
  factory _$CreateCardCoverResponseCopyWith(
    _CreateCardCoverResponse value,
    $Res Function(_CreateCardCoverResponse) _then,
  ) = __$CreateCardCoverResponseCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'paymentLink') String paymentLink,
  });
}

/// @nodoc
class __$CreateCardCoverResponseCopyWithImpl<$Res>
    implements _$CreateCardCoverResponseCopyWith<$Res> {
  __$CreateCardCoverResponseCopyWithImpl(this._self, this._then);

  final _CreateCardCoverResponse _self;
  final $Res Function(_CreateCardCoverResponse) _then;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? code = null, Object? paymentLink = null}) {
    return _then(
      _CreateCardCoverResponse(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Group {
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'logo')
  String get logo;
  @JsonKey(name: 'userCount')
  int get userCount;
  @JsonKey(name: 'creator')
  String get creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GroupCopyWith<Group> get copyWith =>
      _$GroupCopyWithImpl<Group>(this as Group, _$identity);

  /// Serializes this Group to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class $GroupCopyWith<$Res> {
  factory $GroupCopyWith(Group value, $Res Function(Group) _then) =
      _$GroupCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class _$GroupCopyWithImpl<$Res> implements $GroupCopyWith<$Res> {
  _$GroupCopyWithImpl(this._self, this._then);

  final Group _self;
  final $Res Function(Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Group implements Group {
  const _Group({
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'logo') this.logo = '',
    @JsonKey(name: 'userCount') this.userCount = 1,
    @JsonKey(name: 'creator') required this.creatorName,
  });
  factory _Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);

  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'logo')
  final String logo;
  @override
  @JsonKey(name: 'userCount')
  final int userCount;
  @override
  @JsonKey(name: 'creator')
  final String creatorName;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GroupCopyWith<_Group> get copyWith =>
      __$GroupCopyWithImpl<_Group>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GroupToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Group &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.creatorName, creatorName) ||
                other.creatorName == creatorName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    userCount,
    creatorName,
  );

  @override
  String toString() {
    return 'Group(id: $id, name: $name, description: $description, logo: $logo, userCount: $userCount, creatorName: $creatorName)';
  }
}

/// @nodoc
abstract mixin class _$GroupCopyWith<$Res> implements $GroupCopyWith<$Res> {
  factory _$GroupCopyWith(_Group value, $Res Function(_Group) _then) =
      __$GroupCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'userCount') int userCount,
    @JsonKey(name: 'creator') String creatorName,
  });
}

/// @nodoc
class __$GroupCopyWithImpl<$Res> implements _$GroupCopyWith<$Res> {
  __$GroupCopyWithImpl(this._self, this._then);

  final _Group _self;
  final $Res Function(_Group) _then;

  /// Create a copy of Group
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? userCount = null,
    Object? creatorName = null,
  }) {
    return _then(
      _Group(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        creatorName: null == creatorName
            ? _self.creatorName
            : creatorName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$EthccProfile {
  @JsonKey(name: 'githubHandle')
  String get githubHandle;
  @JsonKey(
    name: 'topics',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  List<String> get topics;
  @JsonKey(
    name: 'role',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EthccProfileCopyWith<EthccProfile> get copyWith =>
      _$EthccProfileCopyWithImpl<EthccProfile>(
        this as EthccProfile,
        _$identity,
      );

  /// Serializes this EthccProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EthccProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other.topics, topics) &&
            const DeepCollectionEquality().equals(other.roles, roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(topics),
    const DeepCollectionEquality().hash(roles),
  );

  @override
  String toString() {
    return 'EthccProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class $EthccProfileCopyWith<$Res> {
  factory $EthccProfileCopyWith(
    EthccProfile value,
    $Res Function(EthccProfile) _then,
  ) = _$EthccProfileCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class _$EthccProfileCopyWithImpl<$Res> implements $EthccProfileCopyWith<$Res> {
  _$EthccProfileCopyWithImpl(this._self, this._then);

  final EthccProfile _self;
  final $Res Function(EthccProfile) _then;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _self.copyWith(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self.roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _EthccProfile extends EthccProfile {
  const _EthccProfile({
    @JsonKey(name: 'githubHandle') this.githubHandle = '',
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    final List<String> topics = const [],
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    final List<String> roles = const [],
  }) : _topics = topics,
       _roles = roles,
       super._();
  factory _EthccProfile.fromJson(Map<String, dynamic> json) =>
      _$EthccProfileFromJson(json);

  @override
  @JsonKey(name: 'githubHandle')
  final String githubHandle;
  final List<String> _topics;
  @override
  @JsonKey(
    name: 'topics',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  List<String> get topics {
    if (_topics is EqualUnmodifiableListView) return _topics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topics);
  }

  final List<String> _roles;
  @override
  @JsonKey(
    name: 'role',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles {
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_roles);
  }

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EthccProfileCopyWith<_EthccProfile> get copyWith =>
      __$EthccProfileCopyWithImpl<_EthccProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EthccProfileToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EthccProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other._topics, _topics) &&
            const DeepCollectionEquality().equals(other._roles, _roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(_topics),
    const DeepCollectionEquality().hash(_roles),
  );

  @override
  String toString() {
    return 'EthccProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class _$EthccProfileCopyWith<$Res>
    implements $EthccProfileCopyWith<$Res> {
  factory _$EthccProfileCopyWith(
    _EthccProfile value,
    $Res Function(_EthccProfile) _then,
  ) = __$EthccProfileCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class __$EthccProfileCopyWithImpl<$Res>
    implements _$EthccProfileCopyWith<$Res> {
  __$EthccProfileCopyWithImpl(this._self, this._then);

  final _EthccProfile _self;
  final $Res Function(_EthccProfile) _then;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _EthccProfile(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self._topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self._roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
mixin _$UpdateEthccTopicsRequest {
  @JsonKey(name: 'topics')
  String get topics;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateEthccTopicsRequestCopyWith<UpdateEthccTopicsRequest> get copyWith =>
      _$UpdateEthccTopicsRequestCopyWithImpl<UpdateEthccTopicsRequest>(
        this as UpdateEthccTopicsRequest,
        _$identity,
      );

  /// Serializes this UpdateEthccTopicsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateEthccTopicsRequest &&
            (identical(other.topics, topics) || other.topics == topics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, topics);

  @override
  String toString() {
    return 'UpdateEthccTopicsRequest(topics: $topics)';
  }
}

/// @nodoc
abstract mixin class $UpdateEthccTopicsRequestCopyWith<$Res> {
  factory $UpdateEthccTopicsRequestCopyWith(
    UpdateEthccTopicsRequest value,
    $Res Function(UpdateEthccTopicsRequest) _then,
  ) = _$UpdateEthccTopicsRequestCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'topics') String topics});
}

/// @nodoc
class _$UpdateEthccTopicsRequestCopyWithImpl<$Res>
    implements $UpdateEthccTopicsRequestCopyWith<$Res> {
  _$UpdateEthccTopicsRequestCopyWithImpl(this._self, this._then);

  final UpdateEthccTopicsRequest _self;
  final $Res Function(UpdateEthccTopicsRequest) _then;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? topics = null}) {
    return _then(
      _self.copyWith(
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateEthccTopicsRequest implements UpdateEthccTopicsRequest {
  const _UpdateEthccTopicsRequest({
    @JsonKey(name: 'topics') required this.topics,
  });
  factory _UpdateEthccTopicsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateEthccTopicsRequestFromJson(json);

  @override
  @JsonKey(name: 'topics')
  final String topics;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateEthccTopicsRequestCopyWith<_UpdateEthccTopicsRequest> get copyWith =>
      __$UpdateEthccTopicsRequestCopyWithImpl<_UpdateEthccTopicsRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateEthccTopicsRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateEthccTopicsRequest &&
            (identical(other.topics, topics) || other.topics == topics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, topics);

  @override
  String toString() {
    return 'UpdateEthccTopicsRequest(topics: $topics)';
  }
}

/// @nodoc
abstract mixin class _$UpdateEthccTopicsRequestCopyWith<$Res>
    implements $UpdateEthccTopicsRequestCopyWith<$Res> {
  factory _$UpdateEthccTopicsRequestCopyWith(
    _UpdateEthccTopicsRequest value,
    $Res Function(_UpdateEthccTopicsRequest) _then,
  ) = __$UpdateEthccTopicsRequestCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'topics') String topics});
}

/// @nodoc
class __$UpdateEthccTopicsRequestCopyWithImpl<$Res>
    implements _$UpdateEthccTopicsRequestCopyWith<$Res> {
  __$UpdateEthccTopicsRequestCopyWithImpl(this._self, this._then);

  final _UpdateEthccTopicsRequest _self;
  final $Res Function(_UpdateEthccTopicsRequest) _then;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? topics = null}) {
    return _then(
      _UpdateEthccTopicsRequest(
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UpdateEthccGithubRequest {
  @JsonKey(name: 'githubHandle')
  String get githubHandle;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateEthccGithubRequestCopyWith<UpdateEthccGithubRequest> get copyWith =>
      _$UpdateEthccGithubRequestCopyWithImpl<UpdateEthccGithubRequest>(
        this as UpdateEthccGithubRequest,
        _$identity,
      );

  /// Serializes this UpdateEthccGithubRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateEthccGithubRequest &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, githubHandle);

  @override
  String toString() {
    return 'UpdateEthccGithubRequest(githubHandle: $githubHandle)';
  }
}

/// @nodoc
abstract mixin class $UpdateEthccGithubRequestCopyWith<$Res> {
  factory $UpdateEthccGithubRequestCopyWith(
    UpdateEthccGithubRequest value,
    $Res Function(UpdateEthccGithubRequest) _then,
  ) = _$UpdateEthccGithubRequestCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'githubHandle') String githubHandle});
}

/// @nodoc
class _$UpdateEthccGithubRequestCopyWithImpl<$Res>
    implements $UpdateEthccGithubRequestCopyWith<$Res> {
  _$UpdateEthccGithubRequestCopyWithImpl(this._self, this._then);

  final UpdateEthccGithubRequest _self;
  final $Res Function(UpdateEthccGithubRequest) _then;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? githubHandle = null}) {
    return _then(
      _self.copyWith(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateEthccGithubRequest implements UpdateEthccGithubRequest {
  const _UpdateEthccGithubRequest({
    @JsonKey(name: 'githubHandle') required this.githubHandle,
  });
  factory _UpdateEthccGithubRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateEthccGithubRequestFromJson(json);

  @override
  @JsonKey(name: 'githubHandle')
  final String githubHandle;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateEthccGithubRequestCopyWith<_UpdateEthccGithubRequest> get copyWith =>
      __$UpdateEthccGithubRequestCopyWithImpl<_UpdateEthccGithubRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateEthccGithubRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateEthccGithubRequest &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, githubHandle);

  @override
  String toString() {
    return 'UpdateEthccGithubRequest(githubHandle: $githubHandle)';
  }
}

/// @nodoc
abstract mixin class _$UpdateEthccGithubRequestCopyWith<$Res>
    implements $UpdateEthccGithubRequestCopyWith<$Res> {
  factory _$UpdateEthccGithubRequestCopyWith(
    _UpdateEthccGithubRequest value,
    $Res Function(_UpdateEthccGithubRequest) _then,
  ) = __$UpdateEthccGithubRequestCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'githubHandle') String githubHandle});
}

/// @nodoc
class __$UpdateEthccGithubRequestCopyWithImpl<$Res>
    implements _$UpdateEthccGithubRequestCopyWith<$Res> {
  __$UpdateEthccGithubRequestCopyWithImpl(this._self, this._then);

  final _UpdateEthccGithubRequest _self;
  final $Res Function(_UpdateEthccGithubRequest) _then;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? githubHandle = null}) {
    return _then(
      _UpdateEthccGithubRequest(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UserSettings {
  @JsonKey(name: 'fcmAndroid')
  String? get fcmAndroid;
  @JsonKey(name: 'fcmIos')
  String? get fcmIos;
  @JsonKey(name: 'fcmWeb')
  String? get fcmWeb;

  /// Create a copy of UserSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserSettingsCopyWith<UserSettings> get copyWith =>
      _$UserSettingsCopyWithImpl<UserSettings>(
        this as UserSettings,
        _$identity,
      );

  /// Serializes this UserSettings to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserSettings &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIos, fcmIos) || other.fcmIos == fcmIos) &&
            (identical(other.fcmWeb, fcmWeb) || other.fcmWeb == fcmWeb));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIos, fcmWeb);

  @override
  String toString() {
    return 'UserSettings(fcmAndroid: $fcmAndroid, fcmIos: $fcmIos, fcmWeb: $fcmWeb)';
  }
}

/// @nodoc
abstract mixin class $UserSettingsCopyWith<$Res> {
  factory $UserSettingsCopyWith(
    UserSettings value,
    $Res Function(UserSettings) _then,
  ) = _$UserSettingsCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid') String? fcmAndroid,
    @JsonKey(name: 'fcmIos') String? fcmIos,
    @JsonKey(name: 'fcmWeb') String? fcmWeb,
  });
}

/// @nodoc
class _$UserSettingsCopyWithImpl<$Res> implements $UserSettingsCopyWith<$Res> {
  _$UserSettingsCopyWithImpl(this._self, this._then);

  final UserSettings _self;
  final $Res Function(UserSettings) _then;

  /// Create a copy of UserSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fcmAndroid = freezed,
    Object? fcmIos = freezed,
    Object? fcmWeb = freezed,
  }) {
    return _then(
      _self.copyWith(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIos: freezed == fcmIos
            ? _self.fcmIos
            : fcmIos // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmWeb: freezed == fcmWeb
            ? _self.fcmWeb
            : fcmWeb // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserSettings implements UserSettings {
  const _UserSettings({
    @JsonKey(name: 'fcmAndroid') this.fcmAndroid = '',
    @JsonKey(name: 'fcmIos') this.fcmIos = '',
    @JsonKey(name: 'fcmWeb') this.fcmWeb = '',
  });
  factory _UserSettings.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsFromJson(json);

  @override
  @JsonKey(name: 'fcmAndroid')
  final String? fcmAndroid;
  @override
  @JsonKey(name: 'fcmIos')
  final String? fcmIos;
  @override
  @JsonKey(name: 'fcmWeb')
  final String? fcmWeb;

  /// Create a copy of UserSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserSettingsCopyWith<_UserSettings> get copyWith =>
      __$UserSettingsCopyWithImpl<_UserSettings>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserSettingsToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserSettings &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIos, fcmIos) || other.fcmIos == fcmIos) &&
            (identical(other.fcmWeb, fcmWeb) || other.fcmWeb == fcmWeb));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIos, fcmWeb);

  @override
  String toString() {
    return 'UserSettings(fcmAndroid: $fcmAndroid, fcmIos: $fcmIos, fcmWeb: $fcmWeb)';
  }
}

/// @nodoc
abstract mixin class _$UserSettingsCopyWith<$Res>
    implements $UserSettingsCopyWith<$Res> {
  factory _$UserSettingsCopyWith(
    _UserSettings value,
    $Res Function(_UserSettings) _then,
  ) = __$UserSettingsCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid') String? fcmAndroid,
    @JsonKey(name: 'fcmIos') String? fcmIos,
    @JsonKey(name: 'fcmWeb') String? fcmWeb,
  });
}

/// @nodoc
class __$UserSettingsCopyWithImpl<$Res>
    implements _$UserSettingsCopyWith<$Res> {
  __$UserSettingsCopyWithImpl(this._self, this._then);

  final _UserSettings _self;
  final $Res Function(_UserSettings) _then;

  /// Create a copy of UserSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? fcmAndroid = freezed,
    Object? fcmIos = freezed,
    Object? fcmWeb = freezed,
  }) {
    return _then(
      _UserSettings(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIos: freezed == fcmIos
            ? _self.fcmIos
            : fcmIos // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmWeb: freezed == fcmWeb
            ? _self.fcmWeb
            : fcmWeb // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}
