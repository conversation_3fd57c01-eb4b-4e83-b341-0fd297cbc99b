// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMyCardsHash() => r'3593954662c42ce5345e873b1a8be857f2f3b645';

/// See also [fetchMyCards].
@ProviderFor(fetchMyCards)
final fetchMyCardsProvider = AutoDisposeFutureProvider<List<CardInfo>>.internal(
  fetchMyCards,
  name: r'fetchMyCardsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMyCardsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMyCardsRef = AutoDisposeFutureProviderRef<List<CardInfo>>;
String _$fetchPublicProfileHash() =>
    r'82d1430b0818ebea615a04adecc5de0cec239c71';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPublicProfile].
@ProviderFor(fetchPublicProfile)
const fetchPublicProfileProvider = FetchPublicProfileFamily();

/// See also [fetchPublicProfile].
class FetchPublicProfileFamily extends Family<AsyncValue<UserInfo>> {
  /// See also [fetchPublicProfile].
  const FetchPublicProfileFamily();

  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider call({required String code}) {
    return FetchPublicProfileProvider(code: code);
  }

  @override
  FetchPublicProfileProvider getProviderOverride(
    covariant FetchPublicProfileProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPublicProfileProvider';
}

/// See also [fetchPublicProfile].
class FetchPublicProfileProvider extends AutoDisposeFutureProvider<UserInfo> {
  /// See also [fetchPublicProfile].
  FetchPublicProfileProvider({required String code})
    : this._internal(
        (ref) => fetchPublicProfile(ref as FetchPublicProfileRef, code: code),
        from: fetchPublicProfileProvider,
        name: r'fetchPublicProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchPublicProfileHash,
        dependencies: FetchPublicProfileFamily._dependencies,
        allTransitiveDependencies:
            FetchPublicProfileFamily._allTransitiveDependencies,
        code: code,
      );

  FetchPublicProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String code;

  @override
  Override overrideWith(
    FutureOr<UserInfo> Function(FetchPublicProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPublicProfileProvider._internal(
        (ref) => create(ref as FetchPublicProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserInfo> createElement() {
    return _FetchPublicProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPublicProfileProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPublicProfileRef on AutoDisposeFutureProviderRef<UserInfo> {
  /// The parameter `code` of this provider.
  String get code;
}

class _FetchPublicProfileProviderElement
    extends AutoDisposeFutureProviderElement<UserInfo>
    with FetchPublicProfileRef {
  _FetchPublicProfileProviderElement(super.provider);

  @override
  String get code => (origin as FetchPublicProfileProvider).code;
}

String _$fetchSocialsHash() => r'6bea14e2b1e45e754b9c780f023986ade7714839';

/// See also [fetchSocials].
@ProviderFor(fetchSocials)
const fetchSocialsProvider = FetchSocialsFamily();

/// See also [fetchSocials].
class FetchSocialsFamily extends Family<AsyncValue<List<Social>>> {
  /// See also [fetchSocials].
  const FetchSocialsFamily();

  /// See also [fetchSocials].
  FetchSocialsProvider call({String? code}) {
    return FetchSocialsProvider(code: code);
  }

  @override
  FetchSocialsProvider getProviderOverride(
    covariant FetchSocialsProvider provider,
  ) {
    return call(code: provider.code);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchSocialsProvider';
}

/// See also [fetchSocials].
class FetchSocialsProvider extends AutoDisposeFutureProvider<List<Social>> {
  /// See also [fetchSocials].
  FetchSocialsProvider({String? code})
    : this._internal(
        (ref) => fetchSocials(ref as FetchSocialsRef, code: code),
        from: fetchSocialsProvider,
        name: r'fetchSocialsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchSocialsHash,
        dependencies: FetchSocialsFamily._dependencies,
        allTransitiveDependencies:
            FetchSocialsFamily._allTransitiveDependencies,
        code: code,
      );

  FetchSocialsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.code,
  }) : super.internal();

  final String? code;

  @override
  Override overrideWith(
    FutureOr<List<Social>> Function(FetchSocialsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchSocialsProvider._internal(
        (ref) => create(ref as FetchSocialsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        code: code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Social>> createElement() {
    return _FetchSocialsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchSocialsProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchSocialsRef on AutoDisposeFutureProviderRef<List<Social>> {
  /// The parameter `code` of this provider.
  String? get code;
}

class _FetchSocialsProviderElement
    extends AutoDisposeFutureProviderElement<List<Social>>
    with FetchSocialsRef {
  _FetchSocialsProviderElement(super.provider);

  @override
  String? get code => (origin as FetchSocialsProvider).code;
}

String _$fetchEthccTopicsHash() => r'46e1fdac4bbb31ea1e85124feeb5602cdc0ffd59';

/// See also [fetchEthccTopics].
@ProviderFor(fetchEthccTopics)
final fetchEthccTopicsProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchEthccTopics,
      name: r'fetchEthccTopicsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchEthccTopicsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchEthccTopicsRef = AutoDisposeFutureProviderRef<List<String>>;
String _$fetchEthccRolesHash() => r'64def44aca6c8520c3df3945b2da689092ca810d';

/// See also [fetchEthccRoles].
@ProviderFor(fetchEthccRoles)
final fetchEthccRolesProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      fetchEthccRoles,
      name: r'fetchEthccRolesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchEthccRolesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchEthccRolesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$fetchEthccProfileHash() => r'7936fc236895250ab0b785f108db4d2b2d8faf57';

/// See also [fetchEthccProfile].
@ProviderFor(fetchEthccProfile)
final fetchEthccProfileProvider =
    AutoDisposeFutureProvider<EthccProfile?>.internal(
      fetchEthccProfile,
      name: r'fetchEthccProfileProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$fetchEthccProfileHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchEthccProfileRef = AutoDisposeFutureProviderRef<EthccProfile?>;
String _$fetchGitHubContributionsHash() =>
    r'30d4ff3e8210af34eb904b303e7baf2fd3e425fb';

/// See also [fetchGitHubContributions].
@ProviderFor(fetchGitHubContributions)
const fetchGitHubContributionsProvider = FetchGitHubContributionsFamily();

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsFamily
    extends Family<AsyncValue<FrontEndGitHubContributionCollection?>> {
  /// See also [fetchGitHubContributions].
  const FetchGitHubContributionsFamily();

  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider call({required String handle}) {
    return FetchGitHubContributionsProvider(handle: handle);
  }

  @override
  FetchGitHubContributionsProvider getProviderOverride(
    covariant FetchGitHubContributionsProvider provider,
  ) {
    return call(handle: provider.handle);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGitHubContributionsProvider';
}

/// See also [fetchGitHubContributions].
class FetchGitHubContributionsProvider
    extends AutoDisposeFutureProvider<FrontEndGitHubContributionCollection?> {
  /// See also [fetchGitHubContributions].
  FetchGitHubContributionsProvider({required String handle})
    : this._internal(
        (ref) => fetchGitHubContributions(
          ref as FetchGitHubContributionsRef,
          handle: handle,
        ),
        from: fetchGitHubContributionsProvider,
        name: r'fetchGitHubContributionsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchGitHubContributionsHash,
        dependencies: FetchGitHubContributionsFamily._dependencies,
        allTransitiveDependencies:
            FetchGitHubContributionsFamily._allTransitiveDependencies,
        handle: handle,
      );

  FetchGitHubContributionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.handle,
  }) : super.internal();

  final String handle;

  @override
  Override overrideWith(
    FutureOr<FrontEndGitHubContributionCollection?> Function(
      FetchGitHubContributionsRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGitHubContributionsProvider._internal(
        (ref) => create(ref as FetchGitHubContributionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        handle: handle,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
  createElement() {
    return _FetchGitHubContributionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGitHubContributionsProvider && other.handle == handle;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, handle.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGitHubContributionsRef
    on AutoDisposeFutureProviderRef<FrontEndGitHubContributionCollection?> {
  /// The parameter `handle` of this provider.
  String get handle;
}

class _FetchGitHubContributionsProviderElement
    extends
        AutoDisposeFutureProviderElement<FrontEndGitHubContributionCollection?>
    with FetchGitHubContributionsRef {
  _FetchGitHubContributionsProviderElement(super.provider);

  @override
  String get handle => (origin as FetchGitHubContributionsProvider).handle;
}

String _$validateETHCCProfileHash() =>
    r'cf6907ba83298ddb6507afb7baa34c8057ee0fa4';

/// See also [validateETHCCProfile].
@ProviderFor(validateETHCCProfile)
const validateETHCCProfileProvider = ValidateETHCCProfileFamily();

/// See also [validateETHCCProfile].
class ValidateETHCCProfileFamily extends Family<bool?> {
  /// See also [validateETHCCProfile].
  const ValidateETHCCProfileFamily();

  /// See also [validateETHCCProfile].
  ValidateETHCCProfileProvider call({required bool validateProfile}) {
    return ValidateETHCCProfileProvider(validateProfile: validateProfile);
  }

  @override
  ValidateETHCCProfileProvider getProviderOverride(
    covariant ValidateETHCCProfileProvider provider,
  ) {
    return call(validateProfile: provider.validateProfile);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'validateETHCCProfileProvider';
}

/// See also [validateETHCCProfile].
class ValidateETHCCProfileProvider extends AutoDisposeProvider<bool?> {
  /// See also [validateETHCCProfile].
  ValidateETHCCProfileProvider({required bool validateProfile})
    : this._internal(
        (ref) => validateETHCCProfile(
          ref as ValidateETHCCProfileRef,
          validateProfile: validateProfile,
        ),
        from: validateETHCCProfileProvider,
        name: r'validateETHCCProfileProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$validateETHCCProfileHash,
        dependencies: ValidateETHCCProfileFamily._dependencies,
        allTransitiveDependencies:
            ValidateETHCCProfileFamily._allTransitiveDependencies,
        validateProfile: validateProfile,
      );

  ValidateETHCCProfileProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.validateProfile,
  }) : super.internal();

  final bool validateProfile;

  @override
  Override overrideWith(
    bool? Function(ValidateETHCCProfileRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ValidateETHCCProfileProvider._internal(
        (ref) => create(ref as ValidateETHCCProfileRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        validateProfile: validateProfile,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<bool?> createElement() {
    return _ValidateETHCCProfileProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ValidateETHCCProfileProvider &&
        other.validateProfile == validateProfile;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, validateProfile.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ValidateETHCCProfileRef on AutoDisposeProviderRef<bool?> {
  /// The parameter `validateProfile` of this provider.
  bool get validateProfile;
}

class _ValidateETHCCProfileProviderElement
    extends AutoDisposeProviderElement<bool?>
    with ValidateETHCCProfileRef {
  _ValidateETHCCProfileProviderElement(super.provider);

  @override
  bool get validateProfile =>
      (origin as ValidateETHCCProfileProvider).validateProfile;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
