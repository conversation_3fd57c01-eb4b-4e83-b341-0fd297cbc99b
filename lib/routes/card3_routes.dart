// GENERATED CODE - DO NOT MODIFY MANUALLY
// **************************************************************************
// Auto generated by https://github.com/fluttercandies/ff_annotation_route
// **************************************************************************
// fast mode: true
// version: 11.0.0
// **************************************************************************
// ignore_for_file: duplicate_import,implementation_imports,library_private_types_in_public_api,multiple_combinators,prefer_const_literals_to_create_immutables,unintended_html_in_doc_comment,unnecessary_import,unused_import,unused_local_variable,unused_shown_name,unnecessary_library_name
import 'package:card3/models/business.dart';
import 'package:flutter/foundation.dart';

import '/models/card.dart' show UserInfo;

/// The routeNames auto generated by https://github.com/fluttercandies/ff_annotation_route
const List<String> routeNames = <String>[
  '/',
  '/customize',
  '/fun/connection',
  '/fun/point_record',
  '/fun/referal',
  '/home',
  '/login',
  '/login/email',
  '/nfc',
  '/notification',
  '/setting',
  '/setting/about',
  '/setting/account',
  '/setting/mode',
  '/setting/push',
  '/setting/wallets',
  '/share',
  '/social',
  '/social/profile',
  '/wallet/send',
  '/webview',
];

/// The routes auto generated by https://github.com/fluttercandies/ff_annotation_route
class Routes {
  const Routes._();

  /// '/'
  ///
  /// [name] : '/'
  static const _Root root = _Root();

  /// '/customize'
  ///
  /// [name] : '/customize'
  ///
  /// [constructors] :
  ///
  /// CustomizePage : [String? code]
  static const _Customize customize = _Customize();

  /// '/fun/connection'
  ///
  /// [name] : '/fun/connection'
  static const _FunConnection funConnection = _FunConnection();

  /// '/fun/point_record'
  ///
  /// [name] : '/fun/point_record'
  static const _FunPointRecord funPointRecord = _FunPointRecord();

  /// '/fun/referal'
  ///
  /// [name] : '/fun/referal'
  static const _FunReferal funReferal = _FunReferal();

  /// '/home'
  ///
  /// [name] : '/home'
  static const _Home home = _Home();

  /// '/login'
  ///
  /// [name] : '/login'
  static const _Login login = _Login();

  /// '/login/email'
  ///
  /// [name] : '/login/email'
  static const _LoginEmail loginEmail = _LoginEmail();

  /// '/nfc'
  ///
  /// [name] : '/nfc'
  static const _Nfc nfc = _Nfc();

  /// '/notification'
  ///
  /// [name] : '/notification'
  static const _Notification notification = _Notification();

  /// '/setting'
  ///
  /// [name] : '/setting'
  static const _Setting setting = _Setting();

  /// '/setting/about'
  ///
  /// [name] : '/setting/about'
  static const _SettingAbout settingAbout = _SettingAbout();

  /// '/setting/account'
  ///
  /// [name] : '/setting/account'
  static const _SettingAccount settingAccount = _SettingAccount();

  /// '/setting/mode'
  ///
  /// [name] : '/setting/mode'
  static const _SettingMode settingMode = _SettingMode();

  /// '/setting/push'
  ///
  /// [name] : '/setting/push'
  static const _SettingPush settingPush = _SettingPush();

  /// '/setting/wallets'
  ///
  /// [name] : '/setting/wallets'
  static const _SettingWallets settingWallets = _SettingWallets();

  /// '/share'
  ///
  /// [name] : '/share'
  ///
  /// [constructors] :
  ///
  /// SharePage : [String(required) cardCode]
  static const _Share share = _Share();

  /// '/social'
  ///
  /// [name] : '/social'
  ///
  /// [constructors] :
  ///
  /// SocialPage : [int? socialId, bool isSingleLink, String? action, String? platform, String? currentHandle]
  static const _Social social = _Social();

  /// '/social/profile'
  ///
  /// [name] : '/social/profile'
  ///
  /// [constructors] :
  ///
  /// SocialProfilePage : [UserInfo(required) profile]
  static const _SocialProfile socialProfile = _SocialProfile();

  /// '/wallet/send'
  ///
  /// [name] : '/wallet/send'
  ///
  /// [constructors] :
  ///
  /// SendPage : [TokenBalance? tokenBalance]
  static const _WalletSend walletSend = _WalletSend();

  /// '/webview'
  ///
  /// [name] : '/webview'
  ///
  /// [constructors] :
  ///
  /// WebViewPage : [String(required) url, String? title]
  static const _Webview webview = _Webview();
}

class _Root {
  const _Root();

  String get name => '/';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Customize {
  const _Customize();

  String get name => '/customize';

  Map<String, dynamic> d({
    Key? key,
    String? code,
  }) =>
      <String, dynamic>{
        'key': key,
        'code': code,
      };

  @override
  String toString() => name;
}

class _FunConnection {
  const _FunConnection();

  String get name => '/fun/connection';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _FunPointRecord {
  const _FunPointRecord();

  String get name => '/fun/point_record';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _FunReferal {
  const _FunReferal();

  String get name => '/fun/referal';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Home {
  const _Home();

  String get name => '/home';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Login {
  const _Login();

  String get name => '/login';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _LoginEmail {
  const _LoginEmail();

  String get name => '/login/email';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Nfc {
  const _Nfc();

  String get name => '/nfc';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Notification {
  const _Notification();

  String get name => '/notification';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Setting {
  const _Setting();

  String get name => '/setting';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingAbout {
  const _SettingAbout();

  String get name => '/setting/about';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingAccount {
  const _SettingAccount();

  String get name => '/setting/account';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingMode {
  const _SettingMode();

  String get name => '/setting/mode';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingPush {
  const _SettingPush();

  String get name => '/setting/push';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _SettingWallets {
  const _SettingWallets();

  String get name => '/setting/wallets';

  Map<String, dynamic> d({
    Key? key,
  }) =>
      <String, dynamic>{
        'key': key,
      };

  @override
  String toString() => name;
}

class _Share {
  const _Share();

  String get name => '/share';

  Map<String, dynamic> d({
    Key? key,
    required String cardCode,
  }) =>
      <String, dynamic>{
        'key': key,
        'cardCode': cardCode,
      };

  @override
  String toString() => name;
}

class _Social {
  const _Social();

  String get name => '/social';

  Map<String, dynamic> d({
    Key? key,
    int? socialId,
    bool isSingleLink = false,
    String? action,
    String? platform,
    String? currentHandle,
  }) =>
      <String, dynamic>{
        'key': key,
        'socialId': socialId,
        'isSingleLink': isSingleLink,
        'action': action,
        'platform': platform,
        'currentHandle': currentHandle,
      };

  @override
  String toString() => name;
}

class _SocialProfile {
  const _SocialProfile();

  String get name => '/social/profile';

  Map<String, dynamic> d({
    Key? key,
    required UserInfo profile,
  }) =>
      <String, dynamic>{
        'key': key,
        'profile': profile,
      };

  @override
  String toString() => name;
}

class _WalletSend {
  const _WalletSend();

  String get name => '/wallet/send';

  Map<String, dynamic> d({
    Key? key,
    TokenBalance? tokenBalance,
  }) =>
      <String, dynamic>{
        'key': key,
        'tokenBalance': tokenBalance,
      };

  @override
  String toString() => name;
}

class _Webview {
  const _Webview();

  String get name => '/webview';

  Map<String, dynamic> d({
    Key? key,
    required String url,
    String? title,
  }) =>
      <String, dynamic>{
        'key': key,
        'url': url,
        'title': title,
      };

  @override
  String toString() => name;
}
