import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/api/front_end.dart';
import '/provider/card.dart';

@FFRoute(name: '/github/profile')
class GitHubProfilePage extends ConsumerWidget {
  const GitHubProfilePage({super.key, required this.githubHandle});

  final String githubHandle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      title: 'GitHub Profile',
      backgroundColor: ColorName.backgroundColorDark,
      bodyPadding: const EdgeInsets.symmetric(horizontal: 16.0),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 24),
                    _buildProfileHeader(),
                    const SizedBox(height: 32),
                    _buildContributionSection(ref),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: ColorName.dividerColorDark,
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF6B46C1),
                        Color(0xFF8B5CF6),
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.code,
                    size: 30,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    githubHandle,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ColorName.primaryTextColorDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'GitHub Developer',
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorName.captionTextColorDark,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContributionSection(WidgetRef ref) {
    final contributionsAsync = ref.watch(
      fetchGitHubContributionsProvider(handle: githubHandle),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contribution Activity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: ColorName.primaryTextColorDark,
          ),
        ),
        const SizedBox(height: 16),
        contributionsAsync.when(
          data: (contributionCollection) {
            if (contributionCollection == null) {
              return _buildNoDataCard();
            }
            return _buildContributionCard(contributionCollection);
          },
          loading: () => _buildLoadingCard(),
          error: (error, stack) => _buildErrorCard(),
        ),
      ],
    );
  }

  Widget _buildContributionCard(FrontEndGitHubContributionCollection collection) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${collection.calendar.total} contributions in the last year',
                style: const TextStyle(
                  fontSize: 14,
                  color: ColorName.captionTextColorDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GitHubContributionGraph(
            collection: collection,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: ColorName.themeColorDark,
        ),
      ),
    );
  }

  Widget _buildErrorCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load GitHub contributions',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // 可以添加重试逻辑
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorName.themeColorDark,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Retry',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: const Column(
        children: [
          Icon(
            Icons.code_off,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No GitHub contribution data available',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
