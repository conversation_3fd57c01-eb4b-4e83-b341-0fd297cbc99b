import 'dart:async';
import 'dart:io' show Platform;

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:video_player/video_player.dart';

import '/feat/bridge/module/card3.dart';
import '/provider/api.dart';
import '/provider/user.dart' show userRepoProvider;
import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/login')
class Login extends ConsumerStatefulWidget {
  const Login({super.key});

  @override
  ConsumerState<Login> createState() => _LoginState();
}

class _LoginState extends ConsumerState<Login> {
  VideoPlayerController? _vp;
  bool _isVideoReady = false;
  bool _termsAccepted = false;

  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    if (_disposed) {
      return;
    }

    // 先释放旧资源
    await _disposeVideo();

    try {
      // 创建新的视频控制器
      _vp = VideoPlayerController.asset(
        Assets.media.home,
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );

      await _vp!.initialize();

      if (_disposed) {
        await _vp!.dispose();
        return;
      }

      await _vp!.setLooping(true);
      await _vp!.play();

      if (mounted) {
        setState(() {
          _isVideoReady = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isVideoReady = false;
        });
      }
    }
  }

  Future<void> _disposeVideo() async {
    if (_vp != null) {
      await _vp!.pause();
      await _vp!.dispose();
      _vp = null;
    }
  }

  @override
  void dispose() {
    _disposed = true;
    _disposeVideo();
    super.dispose();
  }

  Future<void> _loginAsGuest() {
    return AppLoading.run(() async {
      final prod = envApiUrlService.indexOf('test') > 0 ? false : true;
      final email = prod ? '<EMAIL>' : '<EMAIL>';
      final code = prod ? '846675' : '187202';
      await privyClient.email.sendCode(email);
      final result = await privyClient.email.loginWithCode(code: code, email: email);
      result.fold(
        onSuccess: (user) async {
          try {
            final accessToken = await ref.read(apiServiceProvider).login(token: user.identityToken ?? '', wallet: '');
            await BoxService.updateToken(accessToken);
            meNavigator.pushNamedAndRemoveUntil(Routes.home.name, (_) => false);
          } catch (e) {
            //todo: 退出登录
            await privyClient.logout();
            meNavigator.pushNamedAndRemoveUntil(Routes.login.name, (_) => false);
            Card3ToastUtil.showToast(message: ToastMessages.errorMessage(e.toString()));
          } finally {}
        },
        onFailure: (error) {
          if (mounted && !_disposed) {
            Card3ToastUtil.showToast(message: ToastMessages.errorMessage(error.message));
          }
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // 检测是否显示两个按钮（登录按钮 + 游客登录按钮）
    final showTwoButtons = isAuditing || kDebugMode;
    // 根据按钮数量调整按钮高度
    final buttonHeight = showTwoButtons ? 48.0 : 56.0;

    return Scaffold(
      body: SafeArea(
        top: false,
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF1F1F1),
            borderRadius: BorderRadius.circular(24),
          ),
          margin: const EdgeInsets.all(0),
          child: Column(
            children: [
              // CARD3 像素文字标题 - 紧贴顶部
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                  top: MediaQuery.paddingOf(context).top + 30,
                  left: 30,
                  right: 30,
                ),
                child: const AppLogo(),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    spacing: 16.0,
                    children: [
                      Expanded(
                        child: AnimatedSwitcher(
                          duration: kThemeAnimationDuration,
                          switchInCurve: Curves.easeInOutCubic,
                          switchOutCurve: Curves.easeInOutCubic,
                          child: _isVideoReady && _vp != null && _vp!.value.isInitialized
                              ? AspectRatio(
                                  aspectRatio: _vp!.value.aspectRatio,
                                  child: VideoPlayer(_vp!),
                                )
                              : const SizedBox.expand(),
                        ),
                      ),
                      Column(
                        children: [
                          _buildButton(
                            text: 'Log in / Sign up',
                            color: const Color(0xFF8560FA),
                            height: buttonHeight,
                            onTap: () {
                              if (!_termsAccepted) {
                                Card3ToastUtil.showToast(
                                  message: ToastMessages.loginTermsRequired,
                                );
                                return;
                              }
                              meNavigator.pushNamed(
                                Routes.loginEmail.name,
                              );
                            },
                          ),
                          if (showTwoButtons)
                            _buildButton(
                              text: 'Guest Login',
                              color: const Color(0xFF999999),
                              height: buttonHeight,
                              onTap: () {
                                if (!_termsAccepted) {
                                  Card3ToastUtil.showToast(
                                    message: ToastMessages.loginTermsRequired,
                                  );
                                  return;
                                }
                                _loginAsGuest();
                              },
                            ),
                        ],
                      ),
                      _buildTermsText(),
                    ],
                  ),
                ),
              ),
              // 底部文字 - 使用固定的边距
              const Padding(
                padding: EdgeInsets.only(bottom: 8.0, top: 8.0),
                child: Text(
                  'Powered by Card3',
                  style: TextStyle(
                    color: Color(0xFFBBBBBB),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 提取按钮创建为方法，减少重复代码
  Widget _buildButton({
    required String text,
    required Color color,
    required VoidCallback onTap,
    double height = 56.0,
  }) {
    return Container(
      width: double.infinity,
      height: height,
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Center(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 服务条款文本
  Widget _buildTermsText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 勾选框
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: _termsAccepted,
            onChanged: (value) {
              setState(() {
                _termsAccepted = value ?? false;
              });
            },
            activeColor: const Color(0xFF8560FA),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: 8),

        // 条款文本
        Flexible(
          child: RichText(
            text: TextSpan(
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF757575),
              ),
              children: [
                const TextSpan(text: 'I have read and agreed to '),
                TextSpan(
                  text: 'Terms of Use',
                  style: const TextStyle(
                    color: Color(0xFF8560FA), // 紫色
                    fontWeight: FontWeight.w700,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      launchUrlString(envUrlEULA);
                    },
                ),
                const TextSpan(text: ' and '),
                TextSpan(
                  text: 'Privacy Policy',
                  style: const TextStyle(
                    color: Color(0xFF8560FA), // 紫色
                    fontWeight: FontWeight.w700,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      launchUrlString(envUrlPP);
                    },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _LoginPage extends ConsumerStatefulWidget {
  const _LoginPage(this.headless);

  final HeadlessInAppWebView headless;

  @override
  ConsumerState<_LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<_LoginPage> {
  late StreamSubscription _sub;

  @override
  void initState() {
    super.initState();
    _listenTokenChanges();
  }

  @override
  void dispose() {
    _sub.cancel();
    super.dispose();
  }

  void _listenTokenChanges() {
    _sub = BoxService.watchUser(key: BoxKeys.token).listen((event) {
      if (event.deleted) {
        return;
      }
      if (event.value case final String token when token.isNotEmpty) {
        _fetchUserInfo(token);
      }
    });
  }

  Future<void> _fetchUserInfo(String token) {
    final api = ref.read(apiServiceProvider);
    return AppLoading.run(() async {
      final info = await api.getUserInfo(token: token);
      await ref.read(userRepoProvider.notifier).update(info);
      // await BoxService.updateChainsFromRemote(ref);
      meNavigator.removeNamedAndPushAndRemoveUntil(Routes.home.name);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
      child: InAppWebView(
        headlessWebView: widget.headless,
        initialSettings: _settings,
        gestureRecognizers: {
          // Apply long press gesture recognizer explicitly on Android.
          if (Platform.isAndroid) Factory(() => LongPressGestureRecognizer()),
        },
        onWebViewCreated: (controller) => _onWebViewCreated(context, ref, controller),
        onCreateWindow: _onCreateWindow,
        shouldOverrideUrlLoading: _shouldOverrideUrlLoading,
        onLoadStart: _onLoadStart,
        onConsoleMessage: _onConsoleMessage,
        onReceivedServerTrustAuthRequest: _onReceivedServerTrustAuthRequest,
      ),
    );
  }
}

InAppWebViewSettings get _settings {
  return InAppWebViewSettings(
    isInspectable: !Release.sealed,
    incognito: false,
    applicationNameForUserAgent: 'Card3/${PackageUtil.versionName}',
    horizontalScrollBarEnabled: false,
    preferredContentMode: UserPreferredContentMode.MOBILE,
    useShouldOverrideUrlLoading: true,
    verticalScrollBarEnabled: false,
    useHybridComposition: false,
    mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
    safeBrowsingEnabled: false,
    algorithmicDarkeningAllowed: true,
    allowsInlineMediaPlayback: true,
    isFraudulentWebsiteWarningEnabled: false,
  );
}

final _tagWebView = '🌐 Login WebView';

void _onWebViewCreated(
  BuildContext context,
  WidgetRef ref,
  InAppWebViewController controller,
) {
  defaultCard3Bridge.inject(context, ref, controller);
}

Future<bool> _onCreateWindow(
  InAppWebViewController controller,
  CreateWindowAction createWindowAction,
) async {
  if (createWindowAction.request.url != null) {
    await controller.loadUrl(urlRequest: createWindowAction.request);
    return true;
  }
  return false;
}

NavigationActionPolicy _shouldOverrideUrlLoading(
  InAppWebViewController controller,
  NavigationAction navigationAction,
) {
  final url = navigationAction.request.url;
  if (url?.rawValue == 'about:blank') {
    return NavigationActionPolicy.CANCEL;
  }
  if (url?.scheme case final scheme? when scheme == 'http' || scheme == 'https') {
    return NavigationActionPolicy.ALLOW;
  }
  return NavigationActionPolicy.CANCEL;
}

void _onLoadStart(InAppWebViewController controller, Uri? uri) {
  LogUtil.d(
    'WebView onLoadStart: $uri',
    tag: _tagWebView,
    tagWithTrace: false,
  );
}

void _onConsoleMessage(
  InAppWebViewController controller,
  ConsoleMessage consoleMessage,
) {
  final level = consoleMessage.messageLevel;
  if (level == ConsoleMessageLevel.ERROR) {
    LogUtil.e(
      'Console: ${consoleMessage.message}',
      tag: _tagWebView,
      tagWithTrace: false,
      report: false,
    );
  } else if (level == ConsoleMessageLevel.WARNING) {
    LogUtil.w(
      'Console: ${consoleMessage.message}',
      tag: _tagWebView,
      tagWithTrace: false,
    );
  } else if (level == ConsoleMessageLevel.LOG) {
    LogUtil.d(
      'Console: ${consoleMessage.message}',
      tag: _tagWebView,
      tagWithTrace: false,
    );
  } else if (level == ConsoleMessageLevel.TIP) {
    LogUtil.i(
      'Console: ${consoleMessage.message}',
      tag: _tagWebView,
      tagWithTrace: false,
    );
  } else {
    LogUtil.d(
      'Console: ${consoleMessage.message}',
      tag: _tagWebView,
      tagWithTrace: false,
    );
  }
}

ServerTrustAuthResponse _onReceivedServerTrustAuthRequest(
  InAppWebViewController controller,
  ServerTrustChallenge challenge,
) {
  return ServerTrustAuthResponse(
    action: ServerTrustAuthResponseAction.PROCEED,
  );
}
