import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show UserInfo;

@FFRoute(name: '/social/profile')
class SocialProfilePage extends ConsumerWidget {
  const SocialProfilePage({super.key, required this.profile});

  final UserInfo profile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      backgroundColor: ColorName.backgroundColorDark,
      bodyPadding: EdgeInsets.zero,
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: CustomScrollView(
            slivers: [
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    const SizedBox(height: 40),
                    _buildProfileHeader(),
                    const SizedBox(height: 40),
                    _buildSocialCards(),
                    const SizedBox(height: 40),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Column(
      children: [
        // Avatar
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: ColorName.dividerColorDark,
              width: 2,
            ),
          ),
          child: ClipOval(
            child: profile.avatar.isNotEmpty
                ? Image.network(
                    profile.avatar,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildAvatarPlaceholder(),
                  )
                : _buildAvatarPlaceholder(),
          ),
        ),
        const SizedBox(height: 24),

        // Name
        Text(
          profile.name.isNotEmpty ? profile.name : 'TODO: User Name',
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: ColorName.primaryTextColorDark,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Description
        Text(
          profile.title.isNotEmpty
              ? profile.title
              : 'TODO: NFT player since 2020. Memecoin OG. Investor in 70+ BTC startups.',
          style: const TextStyle(
            fontSize: 16,
            color: ColorName.captionTextColorDark,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAvatarPlaceholder() {
    return Container(
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6B46C1),
            Color(0xFF8B5CF6),
          ],
        ),
      ),
      child: const Icon(
        Icons.person,
        size: 60,
        color: Colors.white,
      ),
    );
  }

  Widget _buildSocialCards() {
    final socialItems = [
      _SocialItem(
        icon: Assets.icons.social.twitter,
        platform: 'X (Twitter)',
        handle: profile.handle.isNotEmpty ? profile.handle : 'TODO: SAPHIRON1992',
        backgroundColor: Colors.black,
        isVerified: profile.handle.isNotEmpty,
      ),
      _SocialItem(
        icon: Assets.icons.social.telegram,
        platform: 'Telegram',
        handle: 'TODO: SAPHIRON1992',
        backgroundColor: const Color(0xFF23A9EA),
        isVerified: false,
      ),
      _SocialItem(
        icon: Assets.icons.social.discord,
        platform: 'Discord',
        handle: 'TODO: SAPHIRON1992',
        backgroundColor: const Color(0xFF5865F2),
        isVerified: false,
      ),
      _SocialItem(
        icon: Assets.icons.social.phone,
        platform: 'Phone',
        handle: profile.userEmail.isNotEmpty ? profile.userEmail : 'TODO: +86 13112231126',
        backgroundColor: ColorName.cardColorDark,
        isVerified: false,
      ),
    ];

    return Column(
      children: socialItems.map((item) => Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildSocialCard(item),
      )).toList(),
    );
  }

  Widget _buildSocialCard(_SocialItem item) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icon container
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: item.backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: item.icon.svg(
                width: 24,
                height: 24,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.platform,
                  style: const TextStyle(
                    fontSize: 14,
                    color: ColorName.captionTextColorDark,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.handle,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: ColorName.primaryTextColorDark,
                        ),
                      ),
                    ),
                    if (item.isVerified)
                      const Icon(
                        Icons.verified,
                        size: 20,
                        color: Color(0xFF1DA1F2),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SocialItem {
  const _SocialItem({
    required this.icon,
    required this.platform,
    required this.handle,
    required this.backgroundColor,
    required this.isVerified,
  });

  final SvgGenImage icon;
  final String platform;
  final String handle;
  final Color backgroundColor;
  final bool isVerified;
}
