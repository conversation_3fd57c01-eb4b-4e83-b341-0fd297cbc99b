import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show UserInfo;

@FFRoute(name: '/social/profile')
class SocialProfilePage extends ConsumerWidget {
  const SocialProfilePage({super.key, required this.profile});

  final UserInfo profile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      body: CustomScrollView(),
    );
  }
}
