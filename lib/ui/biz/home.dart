import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '/feat/link/helper.dart';
import '/provider/settings.dart';
import 'home/card.dart';
import 'home/fun.dart';
import 'home/wallet.dart';

final drawerGlobalKeyProvider = Provider<GlobalKey<ScaffoldState>>((ref) {
  return GlobalKey<ScaffoldState>();
});

@FFRoute(name: '/home')
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with WidgetsBindingObserver {
  String? _clipboardText;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _handleClipboardData();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _handleClipboardData() async {
    if (!mounted) {
      return;
    }
    if (!ref.read(settingsProvider).autoParseFromClipboard) {
      return;
    }
    final data = await Clipboard.getData('text/plain');
    final text = data?.text?.trim();

    if (text == _clipboardText) {
      return;
    }
    _clipboardText = text;

    if (text == null || text.isEmpty) {
      return;
    }
    if (Uri.tryParse(text) case final uri?) {
      AppLinkHelper.handleUri(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: ref.watch(drawerGlobalKeyProvider),
      extendBody: true,
      // 让body延伸到底部导航栏后面
      extendBodyBehindAppBar: false,
      // 不需要延伸到AppBar后面
      body: const _MainBody(),
      bottomNavigationBar: const _BottomNavBar(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(selectedIndexProvider);
    final children = [
      const Home(),
      const Fun(),
      const Wallet(),
    ];
    return IndexedStack(
      index: index,
      children: children,
    );
  }
}

class _BottomNavBar extends ConsumerWidget {
  const _BottomNavBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIndexProvider);

    // 导航选项配置
    final navOptions = [
      // 首页
      {
        'icon': Assets.icons.navCard,
        'label': context.l10n.labelNavDiscovery,
        'index': 0,
      },
      // 中间凸起按钮
      {
        'icon': Assets.icons.navFun,
        'label': context.l10n.labelNavSocial,
        'index': 1,
      },
      // 钱包
      {
        'icon': Assets.icons.navWallet,
        'label': context.l10n.labelNavWallet,
        'index': 2,
      },
    ];

    return Material(
      elevation: selected == 1 ? 5 : 1,
      shadowColor: selected == 1 ? ColorName.cardColorDark : Colors.black.withValues(alpha: 0.5),
      color: selected == 1 ? ColorName.cardColorDark : Colors.white,
      child: Container(
        height: 64 + MediaQuery.of(context).padding.bottom,
        decoration: BoxDecoration(
          color: selected == 1 ? ColorName.cardColorDark : Colors.white,
          boxShadow: [
            BoxShadow(
              color: selected == 1 ? Colors.black : Colors.black.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: selected == 1 ? 5 : 4,
              offset: const Offset(0, -1),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 普通导航按钮 - 移除额外的Padding
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // 第一个导航按钮
                _buildNavItem(
                  context,
                  ref,
                  navOptions[0]['icon'] as SvgGenImage,
                  navOptions[0]['label'] as String,
                  0,
                  selected,
                ),

                // 中间占位
                const SizedBox(width: 90),

                // 第三个导航按钮
                _buildNavItem(
                  context,
                  ref,
                  navOptions[2]['icon'] as SvgGenImage,
                  navOptions[2]['label'] as String,
                  2,
                  selected,
                ),
              ],
            ),

            // 中间凸起按钮
            Positioned(
              top: -20,
              left: 0,
              right: 0,
              child: Center(
                child: _buildCenterButton(
                  context,
                  ref,
                  navOptions[1]['icon'] as SvgGenImage,
                  navOptions[1]['label'] as String,
                  1,
                  selected,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建普通导航项
  Widget _buildNavItem(
    BuildContext context,
    WidgetRef ref,
    SvgGenImage icon,
    String label,
    int index,
    int selected,
  ) {
    final isSelected = index == selected;

    return GestureDetector(
      onTap: () {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 10),
            width: 80,
            height: 48,
            decoration: BoxDecoration(
              color: isSelected ? context.themeColor : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: icon.svg(
                width: 30,
                height: 30,
                colorFilter: ColorFilter.mode(
                  isSelected ? Colors.white : const Color(0xFF9C9CA4),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
        ],
      ),
    );
  }

  // 构建中间凸起按钮
  Widget _buildCenterButton(
    BuildContext context,
    WidgetRef ref,
    SvgGenImage icon,
    String label,
    int index,
    int selected,
  ) {
    final isSelected = index == selected;

    return GestureDetector(
      onTap: () {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      // 最外层容器，使用Stack来控制层级
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 1. 最底层：带阴影的容器（只负责显示阴影）
          Container(
            width: 90,
            height: 90,
            decoration: BoxDecoration(
              color: Colors.transparent, // 透明色以便只显示阴影
              borderRadius: BorderRadius.circular(45),
              boxShadow: [
                BoxShadow(
                  color: isSelected ? Colors.black : Colors.black.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: isSelected ? 5 : 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
          ),

          // 2. 中层：连接导航栏的背景
          Positioned(
            top: 20,
            left: -5,
            width: 100,
            height: 90,
            child: Container(
              color: isSelected ? ColorName.cardColorDark : Colors.white,
            ),
          ),

          // 3. 顶层：主体圆形容器（包含图标）
          Container(
            width: 90,
            height: 90,
            decoration: BoxDecoration(
              color: isSelected ? ColorName.cardColorDark : Colors.white,
              borderRadius: BorderRadius.circular(45),
            ),
            padding: const EdgeInsets.all(10),
            child: Center(
              child: Container(
                width: 70,
                height: 70,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFFFFDE4F) : const Color(0xFF9C9CA4),
                  borderRadius: BorderRadius.circular(35),
                ),
                child: Center(
                  child: icon.svg(
                    width: 40,
                    height: 40,
                    colorFilter: ColorFilter.mode(
                      isSelected ? const Color(0xFF000000) : const Color(0xFFFFFFFF),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
