import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/feat/link/helper.dart';
import '/feat/nfc/handle.dart';
import '/feat/nfc/helper.dart';
import '/models/card.dart';
import '/provider/api.dart';
import '/provider/business.dart';
import '/provider/card.dart';
import '/provider/user.dart';
import '/ui/widgets/card_cover.dart';
import '/ui/widgets/social/data.dart';
import '/ui/widgets/social/profile/avatar.dart';
import '/ui/widgets/social/profile/description.dart';
import '/ui/widgets/social/social_grid.dart';
import '/ui/widgets/topic.dart';

class Home extends ConsumerStatefulWidget {
  const Home({super.key});

  @override
  ConsumerState<Home> createState() => _HomeState();
}

class _HomeState extends ConsumerState<Home> {
  @override
  void initState() {
    super.initState();

    // 设置全局回调函数，处理卡片激活
    AppLinkHelper.setCardActivationCallback((cardCode, activeCode) {
      _handleCardActivation(cardCode, activeCode);
    });
  }

  @override
  void dispose() {
    // 清除全局回调函数
    AppLinkHelper.clearCardActivationCallback();
    super.dispose();
  }

  // 处理卡片激活事件
  Future<void> _handleCardActivation(String cardCode, String activeCode) async {
    final card = await ref.read(apiServiceProvider).getCard(cardCode: cardCode);
    if (card.isActive) {
      Card3ToastUtil.showToast(
        message: ToastMessages.cardAlreadyActivated,
        duration: const Duration(seconds: 3),
      );
    } else {
      // 显示激活弹窗
      _showActivateCardSheet(
        context: context,
        ref: ref,
        params: NfcCardParams(uid: cardCode, ctr: '', cmac: '', activeCode: activeCode),
        card: card,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final userResult = ref.watch(fetchUserInfoProvider);
    final userInfo = userResult.valueOrNull;
    final cardsResult = ref.watch(fetchMyCardsProvider);
    final cards = cardsResult.valueOrNull;
    final socialsResult = ref.watch(fetchSocialsProvider);
    final socials = socialsResult.valueOrNull;

    final filteredList = cards?.list.where((card) => !card.virtualCard).toList();
    final isDirectLink = userInfo?.currentType == 1;
    final config = ref.watch(configProvider);

    final isEthccMode = getIsEthccMode(
      config?.ethccEventIds,
      cards?.list.map((e) => e.card3EventId).toList(),
      userInfo?.profileMode,
      true,
    );

    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.only(
          bottom: 24,
        ),
        child: Column(
          children: [
            // 顶部栏
            _topNav(context: context, ref: ref, cards: filteredList, filteredList: filteredList),

            // 个人资料卡片
            _socialProfileCard(
              context: context,
              userInfo: userInfo,
              isDirectLink: isDirectLink,
            ),
            if (isEthccMode)
              const TopicWidget()
            else
              const SizedBox(
                height: 8,
              ),
            // 社交链接部分
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // 标题和添加按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Social Links',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            // 允许自定义高度
                            useSafeArea: true,
                            // 使用安全区域
                            backgroundColor: Colors.white,
                            constraints: BoxConstraints(
                              maxHeight: MediaQuery.of(context).size.height * 0.7,
                            ),
                            // 内边距为0，让内容决定自己的内边距
                            clipBehavior: Clip.antiAlias,
                            shape: RoundedRectangleBorder(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(20.0),
                              ),
                              side: BorderSide(
                                color: context.theme.dividerColor,
                              ),
                            ),
                            builder: (context) => _SocialDialog(),
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: ColorName.primaryTextColorLight,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // 根据社交数据的加载状态显示不同的内容
                  socialsResult.when(
                    loading: () => const Padding(
                      padding: EdgeInsets.symmetric(vertical: 24.0),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        'load social links failed: $error',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                    data: (socialsData) {
                      if (socialsData.list.isNotEmpty) {
                        // 社交链接列表
                        return Container(
                          margin: const EdgeInsets.only(top: 24),
                          child: Column(
                            children: List.generate(
                              socialsData.list.length,
                              (index) {
                                final social = socialsData.list[index];
                                // 查找当前平台配置
                                final currentPlatform = socialPlatformOptions.firstWhere(
                                  (option) => option.name.displayName == social.platformName,
                                  orElse: () => socialPlatformOptions.first,
                                );

                                return GestureDetector(
                                  onTap: () {
                                    final isSingleLink =
                                        userInfo?.currentType == 1 &&
                                        userInfo?.redirectUrl ==
                                            '${socials?.list.first.platformUrl}'
                                                '${socials?.list.first.handleName}';
                                    ref.read(socialSelectedProvider.notifier).state = social;
                                    ref
                                            .read(
                                              socialSelectedPlatformProvider.notifier,
                                            )
                                            .state =
                                        currentPlatform;
                                    meNavigator.pushNamed(
                                      Routes.social.name,
                                      arguments: Routes.social.d(
                                        socialId: social.id,
                                        isSingleLink: isSingleLink,
                                      ),
                                    );
                                  },
                                  child: Opacity(
                                    opacity: isDirectLink
                                        ? userInfo?.redirectUrl == '${social.platformUrl}${social.handleName}'
                                              ? 1.0
                                              : 0.4
                                        : 1.0,
                                    child: Container(
                                      margin: const EdgeInsets.only(bottom: 8),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(
                                                vertical: 2,
                                              ),
                                              height: 56,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(16),
                                                border: Border.all(
                                                  color:
                                                      userInfo?.currentType == 1 &&
                                                          userInfo?.redirectUrl ==
                                                              '${social.platformUrl}${social.handleName}'
                                                      ? ColorName.themeColorDark
                                                      : Colors.white,
                                                  width: 3,
                                                ),
                                                color: ColorName.primaryTextColorLight,
                                              ),
                                              child: Row(
                                                children: [
                                                  // 单链接指示器
                                                  if (userInfo?.currentType == 1)
                                                    Container(
                                                      width: 40,
                                                      padding: const EdgeInsets.only(
                                                        left: 8,
                                                      ),
                                                      alignment: Alignment.center,
                                                      child: _TopSort(
                                                        social: social,
                                                        currentPlatform: currentPlatform,
                                                        socialsData: socialsData,
                                                      ),
                                                    ),

                                                  // 平台图标
                                                  Container(
                                                    height: 32,
                                                    width: 32,
                                                    margin: const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: currentPlatform.backgroundColor,
                                                      borderRadius: BorderRadius.circular(
                                                        8,
                                                      ),
                                                    ),
                                                    child: Transform.scale(
                                                      scale: 0.6,
                                                      child: currentPlatform.icon,
                                                    ),
                                                  ),

                                                  // 平台标识
                                                  Expanded(
                                                    child: Text(
                                                      social.platformName == 'Whatsapp' ||
                                                              social.platformName == 'MemeX'
                                                          ? social.platformName
                                                          : social.handleName
                                                                .replaceAll(
                                                                  'tel:+',
                                                                  '',
                                                                )
                                                                .replaceAll(
                                                                  'mailto:',
                                                                  '',
                                                                ),
                                                      style: const TextStyle(
                                                        fontSize: 18,
                                                        color: Colors.white,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),

                                                  // 认证标记
                                                  if (social.isVerify == true)
                                                    Container(
                                                      margin: const EdgeInsets.only(
                                                        right: 12,
                                                      ),
                                                      child: Assets.icons.v.svg(
                                                        width: 24,
                                                        height: 24,
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      } else {
                        // 平台预览（当没有社交链接时）
                        return Container(
                          margin: const EdgeInsets.only(top: 16),
                          height: 40,
                          child: Stack(
                            children: List.generate(
                              socialPlatformOptions.where((o) => o.event == null).take(8).length,
                              (index) {
                                final platform = socialPlatformOptions.where((o) => o.event == null).toList()[index];
                                return Positioned(
                                  left: index * 28.0, // 重叠效果
                                  child: Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      color: platform.backgroundColor,
                                      borderRadius: BorderRadius.circular(16),
                                      gradient: platform.gradient,
                                    ),
                                    child: Center(
                                      child: Transform.scale(
                                        scale: 0.5,
                                        child: platform.icon,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      }
                    },
                  ),

                  // 直接链接开关
                  if (socials?.list case final list? when list.isNotEmpty)
                    Column(
                      children: [
                        const Divider(height: 32),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Direct Link',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'with one single link',
                                  style: TextStyle(
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                            Switch(
                              value: userInfo?.currentType == 1,
                              activeColor: ColorName.themeColorDark,
                              onChanged: (value) => AppLoading.run(() async {
                                // 实现切换单链接逻辑
                                if (value) {
                                  final filteredSocials = socials?.list
                                      .where((o) => o.platformName != 'Phone' && o.platformName != 'Email')
                                      .toList();
                                  final redirectUrl = filteredSocials != null && filteredSocials.isNotEmpty
                                      ? filteredSocials.first.platformUrl + filteredSocials.first.handleName
                                      : '';
                                  await ref
                                      .read(apiServiceProvider)
                                      .updateUserInfo(
                                        redirectUrl: redirectUrl,
                                        currentType: 1,
                                      );
                                  ref.invalidate(fetchUserInfoProvider);
                                } else {
                                  await ref.read(apiServiceProvider).updateUserInfo(currentType: 3);
                                  ref.invalidate(fetchUserInfoProvider);
                                }
                              }),
                            ),
                          ],
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // 添加预览和分享按钮
            Container(
              margin: const EdgeInsets.only(top: 24).copyWith(bottom: 24),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // 预览按钮
                  Expanded(
                    child: Tapper(
                      onTap: () async {
                        final user = userInfo;
                        if (user == null) {
                          return;
                        }
                        if (userInfo?.currentType == 1) {
                          launchUrlString(userInfo?.redirectUrl ?? '');
                          return;
                        }
                        // final profile = await ref.read(apiServiceProvider).getPublicProfile(code: user.referralCode);
                        context.navigator.pushNamed(
                          Routes.socialProfile.name,
                          arguments: Routes.socialProfile.d(profile: user),
                        );
                        return;
                        // final previewUrl =
                        //     '$envUrlSocial/profile'
                        //     '?card_code=${userInfo?.referralCode ?? ''}'
                        //     '&action=href'
                        //     '&preview=1';
                        // LogUtil.d('previewUrl: $previewUrl');
                        // meNavigator.pushNamed(
                        //   Routes.webview.name,
                        //   arguments: Routes.webview.d(
                        //     url: previewUrl,
                        //     title: 'Preview',
                        //   ),
                        // );
                        // launchUrlString(previewUrl);
                        // _showPreviewWebView(
                        //   context,
                        //   userInfo.value?.referralCode ?? '',
                        // );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: ColorName.themeColorDark,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Center(
                          child: Text(
                            'Preview',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: ColorName.themeColorDark,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 分享按钮
                  Expanded(
                    child: Tapper(
                      onTap: () {
                        final cardCode = cards?.list
                            .firstWhere((o) => o.virtualCard, orElse: () => cards.list.last)
                            .cardCode;
                        if (cardCode == null) {
                          return;
                        }
                        meNavigator.pushNamed(
                          Routes.share.name,
                          arguments: Routes.share.d(cardCode: cardCode),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        decoration: BoxDecoration(
                          color: ColorName.themeColorDark,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Share',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(width: 4),
                            Icon(
                              Icons.arrow_outward,
                              color: Colors.white,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 顶部导航栏
  Widget _topNav({
    required BuildContext context,
    required WidgetRef ref,
    List<CardInfo>? cards,
    List<CardInfo>? filteredList,
  }) {
    final userInfo = ref.watch(fetchUserInfoProvider);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧聊天按钮
          Stack(
            clipBehavior: Clip.none,
            children: [
              GestureDetector(
                onTap: () {
                  meNavigator.pushNamed(Routes.notification.name);
                },
                child: Assets.icons.message.svg(
                  width: 36,
                  height: 36,
                  colorFilter: const Color(0xFF3A3A49).filter,
                ),
              ),
              if (userInfo.valueOrNull case final user? when user.lastMessageId != user.latestMessageId)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),

          // 中间卡片计数器
          GestureDetector(
            onTap: () {
              // 显示卡片选择底部弹窗
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                // 允许自定义高度
                useSafeArea: true,
                // 使用安全区域
                backgroundColor: const Color(0xFF3A3A49),
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                // 内边距为0，让内容决定自己的内边距
                clipBehavior: Clip.antiAlias,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(20.0),
                  ),
                ),
                builder: (context) => _CardDrop(cards: filteredList ?? []),
              );
            },
            child: Container(
              height: 44,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                border: Border.all(color: Colors.grey[400]!),
              ),
              child: Row(
                children: [
                  // 卡片文字
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'CARDS',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // 数量和下拉箭头
                  const SizedBox(width: 8),
                  Text(
                    'x ${filteredList?.length ?? 0}',
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.black,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          // NFC扫描按钮
          NfcHandler(
            onResult: (result, success) async {
              if (NfcHelper.isCard3Format(result)) {
                final params = NfcHelper.extractIdentifier(result);
                if (params != null) {
                  final card = await ref.read(apiServiceProvider).getCard(cardCode: params.uid);
                  if (card.isActive) {
                    Card3ToastUtil.showToast(
                      message: ToastMessages.cardAlreadyActivated,
                      duration: const Duration(seconds: 3),
                    );
                  } else {
                    // 显示激活弹窗
                    _showActivateCardSheet(
                      context: context,
                      ref: ref,
                      params: params,
                      card: card,
                    );
                  }
                }
              } else {
                Card3ToastUtil.showToast(
                  message: ToastMessages.invalidCardFormat,
                  duration: const Duration(seconds: 3),
                );
              }
            },
            onStateChanged: (state) {
              LogUtil.d('state: $state');
            },
            child: Assets.icons.nfc.svg(
              colorFilter: const Color(0xFF3A3A49).filter,
              width: 36,
              height: 36,
            ),
          ),
        ],
      ),
    );
  }

  void _showActivateCardSheet({
    required BuildContext context,
    required WidgetRef ref,
    required NfcCardParams params,
    required dynamic card,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20.0),
        ),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题和关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'New item detected',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  color: Colors.black,
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 卡片图像
            SizedBox(
              width: 150,
              height: 220,
              child: CardCover(
                card: card,
                width: 120,
                height: 180,
              ),
            ),

            const SizedBox(height: 10),
            const Text(
              'To be activated',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 30),

            // 激活按钮
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context); // 关闭底部弹窗

                // 使用OverlayEntry替代Dialog，避免导航栈问题
                final overlayState = Overlay.of(context);
                final overlayEntry = OverlayEntry(
                  builder: (context) => Container(
                    color: Colors.black54,
                    alignment: Alignment.center,
                    child: const CircularProgressIndicator(),
                  ),
                );

                // 显示加载指示器
                overlayState.insert(overlayEntry);

                try {
                  // 调用API激活卡片
                  await ref
                      .read(apiServiceProvider)
                      .activeCardByNfc(
                        uid: params.uid,
                        ctr: params.ctr ?? '',
                        cmac: params.cmac ?? '',
                        activeCode: params.activeCode ?? '',
                      );

                  Card3ToastUtil.showToast(
                    message: ToastMessages.cardActivatedSuccessfully,
                  );
                } catch (e) {
                  Card3ToastUtil.showToast(message: ToastMessages.activationError(e.toString()));
                } finally {
                  // 安全移除加载指示器
                  overlayEntry.remove();

                  // 刷新卡片列表
                  ref.invalidate(fetchMyCardsProvider);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorName.themeColorDark,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                'Activate Now',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 个人资料卡片
  Widget _socialProfileCard({
    required BuildContext context,
    required bool isDirectLink,
    UserInfo? userInfo,
  }) {
    return Opacity(
      opacity: isDirectLink ? 0.4 : 1.0,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 卡片主体
            Container(
              margin: const EdgeInsets.only(top: 40),
              padding: const EdgeInsets.only(top: 50, bottom: 16),
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                  ),
                ],
              ),
              child: GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    // 允许自定义高度
                    useSafeArea: true,
                    // 使用安全区域
                    backgroundColor: Colors.white,
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.7,
                    ),
                    // 内边距为0，让内容决定自己的内边距
                    clipBehavior: Clip.antiAlias,
                    shape: RoundedRectangleBorder(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(20.0),
                      ),
                      side: BorderSide(
                        color: context.theme.dividerColor,
                      ),
                    ),
                    builder: (context) => const Description(),
                  );
                },
                child: Column(
                  children: [
                    // 姓名
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        (userInfo?.name ?? '') != '' ? userInfo!.name : 'Name',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: (userInfo?.name ?? '') != '' ? Colors.black : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // 职位
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      child: Text(
                        (userInfo?.title ?? '') != '' ? userInfo!.title : 'Title',
                        style: TextStyle(
                          fontSize: 16,
                          color: (userInfo?.title ?? '') != '' ? Colors.black87 : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    // 公司
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        (userInfo?.company ?? '') != '' ? userInfo!.company : 'Company',
                        style: TextStyle(
                          fontSize: 16,
                          color: (userInfo?.company ?? '') != '' ? Colors.black87 : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // 头像（绝对定位在卡片顶部）
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    // 实际应打开头像编辑弹窗
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      // 允许自定义高度
                      useSafeArea: true,
                      // 使用安全区域
                      backgroundColor: Colors.white,
                      constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height * 0.7,
                      ),
                      // 内边距为0，让内容决定自己的内边距
                      clipBehavior: Clip.antiAlias,
                      shape: RoundedRectangleBorder(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(20.0),
                        ),
                        side: BorderSide(
                          color: context.theme.dividerColor,
                        ),
                      ),
                      builder: (context) => const Avatar(),
                    );
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: userInfo?.avatar != null && userInfo!.avatar != ''
                          ? MEImage(
                              userInfo.avatar,
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            )
                          : Icon(
                              Icons.account_circle,
                              size: 80,
                              color: Colors.grey.shade400,
                            ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示预览网页视图
  // void _showPreviewWebView(BuildContext context, String cardCode) {
  //   final previewUrl = '$envUrlSocial/profile?card_code=$cardCode&action=href&preview=1';

  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.white,
  //     shape: const RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //     ),
  //     builder: (context) {
  //       return FractionallySizedBox(
  //         heightFactor: 0.85,
  //         child: Column(
  //           children: [
  //             // 标题栏
  //             Container(
  //               padding:
  //                   const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //               decoration: BoxDecoration(
  //                 border: Border(
  //                   bottom: BorderSide(color: Colors.grey.shade200),
  //                 ),
  //               ),
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   const Text(
  //                     'Preview',
  //                     style: TextStyle(
  //                       fontSize: 18,
  //                       fontWeight: FontWeight.bold,
  //                     ),
  //                   ),
  //                   IconButton(
  //                     icon: const Icon(Icons.close),
  //                     onPressed: () => Navigator.pop(context),
  //                   ),
  //                 ],
  //               ),
  //             ),

  //             // WebView
  //             Expanded(
  //               child: InAppWebView(
  //                 initialUrlRequest: URLRequest(url: WebUri(previewUrl)),
  //                 initialSettings: InAppWebViewSettings(
  //                   isInspectable: !Release.sealed,
  //                   horizontalScrollBarEnabled: false,
  //                   verticalScrollBarEnabled: false,
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //       );
  //     },
  //   );
  // }
}

class _SocialDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_SocialDialog> createState() => _SocialDialogState();
}

class _SocialDialogState extends ConsumerState<_SocialDialog> {
  @override
  Widget build(BuildContext context) {
    // 获取可用高度
    final availableHeight = MediaQuery.of(context).size.height * 0.85;

    return SizedBox(
      height: availableHeight, // 设置固定高度
      child: Padding(
        padding: const EdgeInsets.all(16).copyWith(bottom: 40),
        child: Column(
          mainAxisSize: MainAxisSize.min, // 不要占用全部空间
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题与关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Add Links',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  icon: const Icon(Icons.close, color: Colors.black, size: 24),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 社交平台网格 - 使用Expanded并包装在滚动容器中
            const Expanded(
              child: SocialGrid(
                disabled: false,
                path: '/social', // 点击后跳转到详情页
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TopSort extends ConsumerWidget {
  const _TopSort({
    required this.social,
    required this.currentPlatform,
    required this.socialsData,
  });

  final Social social;
  final SocialPlatform currentPlatform;
  final ListRep<Social> socialsData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userInfo = ref.watch(fetchUserInfoProvider);

    return Center(
      child: switch (userInfo.valueOrNull?.redirectUrl) {
        final s? when s == '${social.platformUrl}${social.handleName}' => const Icon(
          Icons.check_circle,
          color: Colors.white,
          size: 24,
        ),
        _ => Tapper(
          onTap: () async {
            // 实现向上移动逻辑
            List<Social> moveItemToFirst(List<Social> list, int index) {
              final newList = List<Social>.from(list);
              if (index >= 0 && index < newList.length) {
                final item = newList.removeAt(index);
                newList.insert(0, item);
              }
              return newList;
            }

            if (socialsData.list.isNotEmpty) {
              final int currentIndex = socialsData.list.indexWhere((s) => s.id == social.id);
              if (currentIndex >= 0) {
                final newList = moveItemToFirst(socialsData.list, currentIndex);
                final Map<String, int> sortMap = {};
                for (int i = 0; i < newList.length; i++) {
                  sortMap[newList[i].id.toString()] = newList.length - i;
                }
                await ref.read(apiServiceProvider).socialSort(sorts: sortMap);
                await ref
                    .read(apiServiceProvider)
                    .updateUserInfo(redirectUrl: '${social.platformUrl}${social.handleName}');
                ref.invalidate(fetchSocialsProvider);
                ref.invalidate(fetchUserInfoProvider);
              }
            }
          },
          child: Assets.icons.verticalTop.svg(
            width: 24,
            height: 24,
            colorFilter: const Color(0xFF9C9CA4).filter,
          ),
        ),
      },
    );
  }
}

class _CardDrop extends ConsumerStatefulWidget {
  const _CardDrop({required this.cards});

  final List<CardInfo> cards;

  @override
  ConsumerState<_CardDrop> createState() => _CardDropState();
}

class _CardDropState extends ConsumerState<_CardDrop> {
  @override
  Widget build(BuildContext context) {
    // 获取可用高度
    final availableHeight =
        MediaQuery.of(context).size.height * 0.9 -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom;

    // 按nfcType分组卡片，并使NFC424组在前面
    final nfc424Cards = widget.cards.where((card) => card.nfcType == NfcType.NFC424).toList();
    final nfc215Cards = widget.cards.where((card) => card.nfcType == NfcType.NFC215).toList();

    return Container(
      color: const Color(0xFF3A3A49),
      height: availableHeight, // 设置固定高度
      child: Padding(
        padding: const EdgeInsets.all(16).copyWith(bottom: 40),
        child: Column(
          mainAxisSize: MainAxisSize.min, // 不要占用全部空间
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题与关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'My Card3 Collections',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  icon: const Icon(Icons.close, color: Colors.white, size: 28),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // 卡片列表
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // NFC424卡片组 - Earn Points with Every Use
                    if (nfc424Cards.isNotEmpty) ...[
                      const Row(
                        children: [
                          Text(
                            'V2.0',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildCardGrid(nfc424Cards),
                      if (nfc215Cards.isNotEmpty) ...[
                        const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Divider(
                            height: 1,
                            thickness: 1,
                            color: Color(0xFF4A4A59),
                          ),
                        ),
                      ],
                    ],

                    // NFC215卡片组 - Non-Reward
                    if (nfc215Cards.isNotEmpty) ...[
                      const Text(
                        'V1.0',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildCardGrid(nfc215Cards),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建卡片网格
  Widget _buildCardGrid(List<CardInfo> cards) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.56, // 调整卡片比例更接近参考图
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        final paddedId = card.id.toString().padLeft(8, '0');

        return GestureDetector(
          onTap: () {
            // 选择该卡片并关闭弹窗
            // Navigator.pop(context);
          },
          child: Column(
            children: [
              // 卡片图像容器
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Center(
                    child: card.backCover.isNotEmpty
                        ? MEImage(
                            card.backCover,
                            fit: BoxFit.cover,
                            // width: double.infinity,
                            // height: double.infinity,
                          )
                        : Center(
                            child: card.cardType == CardType.STICKER
                                ? Assets.icons.images.stickerCover.image(
                                    fit: BoxFit.contain,
                                  )
                                : card.cardType == CardType.WRISTBAND
                                ? Assets.icons.images.wristbandCover.image(
                                    fit: BoxFit.contain,
                                  )
                                : Assets.icons.images.normalBackcover.image(
                                    fit: BoxFit.contain,
                                  ),
                          ),
                  ),
                ),
              ),
              // 卡片编号
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  '${paddedId.substring(0, 4)} ${paddedId.substring(paddedId.length - 4)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
