import 'dart:convert';
import 'dart:typed_data';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';

class Description extends ConsumerStatefulWidget {
  const Description({
    super.key,
    this.socialId,
    this.isSingleLink = false,
    this.withAvatar = false,
  });

  final int? socialId;
  final bool isSingleLink;
  final bool withAvatar;

  @override
  ConsumerState<Description> createState() => _DescriptionState();
}

class _DescriptionState extends ConsumerState<Description> {
  late final TextEditingController nameController;
  late final TextEditingController titleController;
  late final TextEditingController companyController;
  late String? _avatarUrl;

  final GlobalKey<AvatarImgPickerState> _pickerKey = GlobalKey<AvatarImgPickerState>();
  bool _isLoading = false;
  Uint8List? _imageBytes;

  @override
  void initState() {
    super.initState();
    _fetchUserInfo();
  }

  @override
  void dispose() {
    nameController.dispose();
    titleController.dispose();
    companyController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    final localUser = ref.read(userRepoProvider)!;
    nameController = TextEditingController(text: localUser.name);
    titleController = TextEditingController(text: localUser.title);
    companyController = TextEditingController(text: localUser.company);
    _avatarUrl = localUser.avatar;

    final userInfo = await ref.read(apiServiceProvider).getUserInfo();
    if (userInfo != localUser) {
      globalContainer
        ..read(userRepoProvider.notifier).update(userInfo)
        ..invalidate(fetchUserInfoProvider);
      if (mounted) {
        setState(() {
          nameController.text = userInfo.name;
          titleController.text = userInfo.title;
          companyController.text = userInfo.company;
          _avatarUrl = userInfo.avatar;
        });
      }
    }
  }

  void _handleImageSelected(String base64Image, Uint8List imageBytes) {
    setState(() {
      _imageBytes = imageBytes;
    });
  }

  Future<void> _handleSubmit() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 如果需要上传头像并且有图片
      if (widget.withAvatar && _imageBytes != null) {
        // 将图片转换为base64格式
        final String base64String = base64Encode(_imageBytes!);
        final String base64Image = 'data:image/jpeg;base64,$base64String';

        // 上传头像
        final avatarUrl = await ref
            .read(apiServiceProvider)
            .uploadAvatar(
              fileContent: base64Image,
            );

        // 更新用户信息，包括头像
        await ref
            .read(apiServiceProvider)
            .updateUserInfo(
              name: nameController.text,
              title: titleController.text,
              company: companyController.text,
              avatar: avatarUrl,
            );
      } else {
        // 只更新用户信息，不更新头像
        await ref
            .read(apiServiceProvider)
            .updateUserInfo(
              name: nameController.text,
              title: titleController.text,
              company: companyController.text,
            );
      }

      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.profileUpdatedSuccessfully);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToUpdateProfile);
      }
      rethrow;
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleClear() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.withAvatar) {
        // 清除所有信息，包括头像
        await ref
            .read(apiServiceProvider)
            .updateUserInfo(
              name: '',
              title: '',
              company: '',
              avatar: '',
            );
      } else {
        // 只清除描述信息
        await ref
            .read(apiServiceProvider)
            .updateUserInfo(
              name: '',
              title: '',
              company: '',
            );
      }

      if (mounted) {
        ref.invalidate(fetchUserInfoProvider);
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.profileCleared);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToClearProfile);
      }
      rethrow;
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(bottom: 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.withAvatar ? 'Edit Profile' : 'Add Descriptions',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 输入区域
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 头像上传区域 (仅在withAvatar模式下显示)
                  if (widget.withAvatar) ...[
                    Center(
                      child: AvatarImgPicker(
                        key: _pickerKey,
                        avatarUrl: _avatarUrl,
                        onImageSelected: _handleImageSelected,
                        size: 120,
                      ),
                    ),
                    const SizedBox(height: 24),
                    if (_imageBytes == null && _avatarUrl == null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: TextButton.icon(
                          onPressed: () {
                            if (_pickerKey.currentState != null) {
                              _pickerKey.currentState!.pickImage();
                            }
                          },
                          icon: const Icon(Icons.photo_library, size: 20),
                          label: const Text(
                            'Upload Photo',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                  ],

                  // 姓名输入框
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFECF0F1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: nameController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Name',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),

                  // 职位输入框
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFECF0F1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: titleController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Title(Optional)',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),

                  // 公司输入框
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFECF0F1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: companyController,
                      style: const TextStyle(
                        fontSize: 24,
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        fillColor: Colors.transparent,
                        hintText: 'Company(Optional)',
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Colors.deepPurpleAccent,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部按钮
          Row(
            children: [
              // 清除按钮
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TextButton(
                  onPressed: _isLoading ? null : _handleClear,
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'Clear',
                    style: TextStyle(
                      color: Colors.red.shade400,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 提交按钮
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF8560FA),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: TextButton(
                    onPressed: _isLoading ? null : _handleSubmit,
                    style: TextButton.styleFrom(
                      backgroundColor: const Color(0xFF8560FA),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                          )
                        : const Text(
                            'Submit',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
          const Gap.v(50.0),
        ],
      ),
    );
  }
}
