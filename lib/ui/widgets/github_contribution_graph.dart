import 'package:flutter/material.dart';

import '/api/front_end.dart'
    show FrontEndGitHubContributionCollection, FrontEndGitHubContributionDays, FrontEndGitHubContributionDay;
import '/res/colors.gen.dart';

class GitHubContributionGraph extends StatelessWidget {
  const GitHubContributionGraph({
    super.key,
    required this.collection,
    this.showMonthLabels = true,
    this.showDayLabels = true,
    this.showLegend = true,
  });

  final FrontEndGitHubContributionCollection collection;
  final bool showMonthLabels;
  final bool showDayLabels;
  final bool showLegend;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth > 600;

        if (isWideScreen) {
          // 宽屏显示：显示完整的贡献图
          return _buildFullGraph();
        } else {
          // 手机屏幕：可滑动的贡献图
          return _buildScrollableGraph();
        }
      },
    );
  }

  Widget _buildFullGraph() {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 800),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showMonthLabels) _buildMonthLabels(),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showDayLabels) _buildDayLabels(),
              Expanded(child: _buildContributionGrid()),
            ],
          ),
          if (showLegend) _buildLegend(),
        ],
      ),
    );
  }

  Widget _buildScrollableGraph() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showMonthLabels) _buildScrollableMonthLabels(),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showDayLabels) _buildDayLabels(),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const ClampingScrollPhysics(),
                child: _buildContributionGrid(),
              ),
            ),
          ],
        ),
        if (showLegend) _buildLegend(),
      ],
    );
  }

  Widget _buildScrollableMonthLabels() {
    return Row(
      children: [
        if (showDayLabels) const SizedBox(width: _dayLabelWidth),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            physics: const ClampingScrollPhysics(),
            child: _buildMonthLabelsContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthLabels() {
    return Row(
      children: [
        if (showDayLabels) const SizedBox(width: _dayLabelWidth),
        Expanded(child: _buildMonthLabelsContent()),
      ],
    );
  }

  Widget _buildMonthLabelsContent() {
    final weeks = collection.calendar.weeks;
    final monthLabels = <Widget>[];

    String? currentMonth;
    int weekIndex = 0;

    for (final week in weeks) {
      if (week.days.isNotEmpty) {
        final firstDay = week.days.first;
        final month = _getMonthAbbreviation(firstDay.date.month);

        if (currentMonth != month) {
          monthLabels.add(
            Positioned(
              left: weekIndex * (_cellSize + _cellSpacing),
              child: Text(
                month,
                style: const TextStyle(
                  fontSize: 12,
                  color: ColorName.captionTextColorDark,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
          currentMonth = month;
        }
      }
      weekIndex++;
    }

    return SizedBox(
      height: 20,
      width: weeks.length * (_cellSize + _cellSpacing),
      child: Stack(children: monthLabels),
    );
  }

  Widget _buildDayLabels() {
    const dayLabels = ['Mon', 'Wed', 'Fri'];

    return SizedBox(
      width: _dayLabelWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 2), // 对齐第一行
          ...List.generate(7, (index) {
            final shouldShow = index == 1 || index == 3 || index == 5; // Mon, Wed, Fri
            return Container(
              height: _cellSize,
              margin: const EdgeInsets.only(bottom: _cellSpacing),
              alignment: Alignment.centerRight,
              child: shouldShow
                  ? Text(
                      dayLabels[index ~/ 2],
                      style: const TextStyle(
                        fontSize: 12,
                        color: ColorName.captionTextColorDark,
                      ),
                    )
                  : null,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildContributionGrid() {
    final weeks = collection.calendar.weeks;

    return SizedBox(
      width: weeks.length * (_cellSize + _cellSpacing),
      child: Row(
        children: weeks.map((week) => _buildWeekColumn(week)).toList(),
      ),
    );
  }

  Widget _buildWeekColumn(FrontEndGitHubContributionDays week) {
    return Container(
      margin: const EdgeInsets.only(right: _cellSpacing),
      child: Column(
        children: week.days.map((day) => _buildDayCell(day)).toList(),
      ),
    );
  }

  Widget _buildDayCell(FrontEndGitHubContributionDay day) {
    final intensity = _getContributionIntensity(day.contributionCount);

    return Container(
      width: _cellSize,
      height: _cellSize,
      margin: const EdgeInsets.only(bottom: _cellSpacing),
      decoration: BoxDecoration(
        color: _getContributionColor(intensity),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildLegend() {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Learn how we count contributions',
            style: TextStyle(
              fontSize: 12,
              color: ColorName.captionTextColorDark,
            ),
          ),
          Row(
            children: [
              const Text(
                'Less',
                style: TextStyle(
                  fontSize: 12,
                  color: ColorName.captionTextColorDark,
                ),
              ),
              const SizedBox(width: 8),
              ...List.generate(5, (index) {
                return Container(
                  width: 10,
                  height: 10,
                  margin: const EdgeInsets.only(left: 2),
                  decoration: BoxDecoration(
                    color: _getContributionColor(index),
                    borderRadius: BorderRadius.circular(2),
                  ),
                );
              }),
              const SizedBox(width: 8),
              const Text(
                'More',
                style: TextStyle(
                  fontSize: 12,
                  color: ColorName.captionTextColorDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 常量定义
  static const double _cellSize = 11.0;
  static const double _cellSpacing = 3.0;
  static const double _dayLabelWidth = 30.0;

  // 辅助方法
  int _getContributionIntensity(int count) {
    if (count == 0) {
      return 0;
    }
    if (count <= 3) {
      return 1;
    }
    if (count <= 6) {
      return 2;
    }
    if (count <= 9) {
      return 3;
    }
    return 4;
  }

  Color _getContributionColor(int intensity) {
    switch (intensity) {
      case 0:
        return ColorName.dividerColorDark;
      case 1:
        return const Color(0xFF0E4429);
      case 2:
        return const Color(0xFF006D32);
      case 3:
        return const Color(0xFF26A641);
      case 4:
        return const Color(0xFF39D353);
      default:
        return ColorName.dividerColorDark;
    }
  }

  String _getMonthAbbreviation(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }
}

/// 简化版 GitHub 贡献图组件，用于嵌入其他页面
class GitHubContributionGraphCompact extends StatelessWidget {
  const GitHubContributionGraphCompact({
    super.key,
    required this.contributionCollection,
    this.height = 120,
  });

  final FrontEndGitHubContributionCollection contributionCollection;
  final double height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const ClampingScrollPhysics(),
        child: _buildCompactGrid(),
      ),
    );
  }

  Widget _buildCompactGrid() {
    final weeks = contributionCollection.calendar.weeks;

    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: weeks.map((week) => _buildCompactWeekColumn(week)).toList(),
      ),
    );
  }

  Widget _buildCompactWeekColumn(FrontEndGitHubContributionDays week) {
    return Container(
      margin: const EdgeInsets.only(right: 2),
      child: Column(
        children: week.days.map((day) => _buildCompactDayCell(day)).toList(),
      ),
    );
  }

  Widget _buildCompactDayCell(FrontEndGitHubContributionDay day) {
    final intensity = _getContributionIntensity(day.contributionCount);

    return Container(
      width: 8,
      height: 8,
      margin: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: _getContributionColor(intensity),
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  int _getContributionIntensity(int count) {
    if (count == 0) {
      return 0;
    }
    if (count <= 3) {
      return 1;
    }
    if (count <= 6) {
      return 2;
    }
    if (count <= 9) {
      return 3;
    }
    return 4;
  }

  Color _getContributionColor(int intensity) {
    switch (intensity) {
      case 0:
        return ColorName.dividerColorDark;
      case 1:
        return const Color(0xFF0E4429);
      case 2:
        return const Color(0xFF006D32);
      case 3:
        return const Color(0xFF26A641);
      case 4:
        return const Color(0xFF39D353);
      default:
        return ColorName.dividerColorDark;
    }
  }
}
