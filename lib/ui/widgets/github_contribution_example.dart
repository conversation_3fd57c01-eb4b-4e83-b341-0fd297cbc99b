import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/api/front_end.dart';
import '/provider/card.dart';
import '/ui/widgets/github_contribution_graph.dart';

/// GitHub 贡献图使用示例
class GitHubContributionExample extends ConsumerWidget {
  const GitHubContributionExample({
    super.key,
    required this.githubHandle,
    this.showCompact = false,
  });

  final String githubHandle;
  final bool showCompact;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final contributionsAsync = ref.watch(
      fetchGitHubContributionsProvider(handle: githubHandle),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.code,
                color: ColorName.primaryTextColorDark,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'GitHub Contributions',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: ColorName.primaryTextColorDark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          contributionsAsync.when(
            data: (contributionCollection) {
              if (contributionCollection == null) {
                return _buildNoDataWidget();
              }
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${contributionCollection.calendar.total} contributions in the last year',
                    style: const TextStyle(
                      fontSize: 14,
                      color: ColorName.captionTextColorDark,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (showCompact)
                    GitHubContributionGraphCompact(
                      contributionCollection: contributionCollection,
                    )
                  else
                    GitHubContributionGraph(
                      collection: contributionCollection,
                    ),
                ],
              );
            },
            loading: () => _buildLoadingWidget(),
            error: (error, stack) => _buildErrorWidget(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const SizedBox(
      height: 120,
      child: Center(
        child: CircularProgressIndicator(
          color: ColorName.themeColorDark,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.grey,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load contributions',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataWidget() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.code_off,
              color: Colors.grey,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'No contribution data available',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
